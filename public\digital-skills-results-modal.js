/**
 * Digital Skills Assessment Results Modal
 * Professional modal for displaying detailed digital skills assessment analysis
 * Matches skills gap modal dimensions and styling
 */

(function() {
    'use strict';

    let isModalInitialized = false;
    let currentDigitalSkillsData = null;

    // Public API
    window.DigitalSkillsResultsModal = {
        show: showDigitalSkillsResultsModal,
        hide: hideModal
    };

    /**
     * Show digital skills results modal
     */
    async function showDigitalSkillsResultsModal(digitalSkillsData, userEmail, userName, userCompany) {
        try {
            // Show loading overlay
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay();
            }

            // Fetch comprehensive digital skills data from Firebase
            const assessmentData = await fetchDigitalSkillsData(userEmail, userCompany);
            
            if (!assessmentData) {
                throw new Error('No digital skills assessment data found');
            }

            currentDigitalSkillsData = assessmentData;

            if (isModalInitialized) {
                await resetAndShowModal(userName);
                return;
            }

            // Create modal
            await createModal(userName);
            isModalInitialized = true;

        } catch (error) {
            console.error('Error showing digital skills results modal:', error);
            if (typeof showErrorMessage === 'function') {
                showErrorMessage('Failed to load digital skills assessment details');
            }
        } finally {
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
        }
    }

    /**
     * Fetch digital skills assessment data from Firebase
     */
    async function fetchDigitalSkillsData(userEmail, userCompany) {
        try {
            const db = firebase.firestore();
            const userDoc = await db.collection('companies')
                .doc(userCompany || 'Birmingham')
                .collection('users')
                .doc(userEmail)
                .get();

            if (!userDoc.exists) {
                throw new Error('User document not found');
            }

            const userData = userDoc.data();
            
            if (!userData.digitalSkillsAssessmentCompleted) {
                throw new Error('Digital skills assessment not completed');
            }

            return userData;

        } catch (error) {
            console.error('Error fetching digital skills data:', error);
            throw error;
        }
    }

    /**
     * Create modal structure
     */
    async function createModal(userName) {
        const overlay = document.createElement('div');
        overlay.id = 'digital-skills-results-overlay';
        overlay.className = 'modal-overlay';
        overlay.innerHTML = createModalHTML(userName);

        document.body.appendChild(overlay);

        // Initialize event listeners
        initializeEventListeners(overlay);

        // Show modal with animation
        requestAnimationFrame(() => {
            overlay.style.opacity = '1';
            const modalContent = overlay.querySelector('.digital-skills-modal-content');
            if (modalContent) {
                modalContent.style.transform = 'scale(1)';
                modalContent.style.opacity = '1';
            }
        });
    }

    /**
     * Reset and show existing modal
     */
    async function resetAndShowModal(userName) {
        const overlay = document.getElementById('digital-skills-results-overlay');
        if (!overlay) return;

        overlay.innerHTML = createModalHTML(userName);
        initializeEventListeners(overlay);

        overlay.style.display = 'flex';
        overlay.style.opacity = '1';
        const modalContent = overlay.querySelector('.digital-skills-modal-content');
        if (modalContent) {
            modalContent.style.transform = 'scale(1)';
            modalContent.style.opacity = '1';
        }
    }

    /**
     * Create modal HTML content
     */
    function createModalHTML(userName) {
        const data = currentDigitalSkillsData;
        
        // Extract key information
        const currentLevel = data.digitalSkillsCurrentLevel || 'Not specified';
        const overallScore = data.digitalSkillsOverallScore || 0;
        const skillsLevel = data.digitalSkillsSkillsLevel || 'Not determined';
        const highestLevelCompleted = data.digitalSkillsHighestLevelCompleted || 'None';
        const completedAt = data.digitalSkillsAssessmentTimestamp;
        const timeSpent = data.totalTimeSpentOnDigitalSkills || 0;
        
        // Format date and time
        const formattedDate = completedAt ? 
            (completedAt.toDate ? completedAt.toDate() : new Date(completedAt)).toLocaleDateString() : 
            'Not available';
        const formattedTime = formatTime(timeSpent);

        // Determine pass status
        const passStatus = determinePassStatus(overallScore, currentLevel);
        
        return `
            <div class="digital-skills-modal-content">
                <div class="digital-skills-modal-header">
                    <div class="digital-skills-modal-title-container">
                        <h2 class="digital-skills-modal-student-title">${userName}</h2>
                        <h3 class="digital-skills-modal-subtitle">Digital Skills Assessment Results</h3>
                    </div>
                    <div class="digital-skills-modal-actions">
                        <button id="export-digital-skills-data" class="digital-skills-export-button">Export Data</button>
                        <button id="close-digital-skills-modal" class="digital-skills-close-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="digital-skills-modal-body">
                    ${createScoreOverviewSection(overallScore, currentLevel, skillsLevel, passStatus, formattedDate, formattedTime)}
                    ${createLevelProgressionSection(data)}
                    ${createAIAnalysisSection(data)}
                    ${createCourseRecommendationSection(data)}
                    ${createStrengthsImprovementsSection(data)}
                    ${createTimeAnalyticsSection(timeSpent, completedAt)}
                    ${createNextStepsSection(skillsLevel, passStatus)}
                </div>
            </div>
        `;
    }

    /**
     * Create score overview section
     */
    function createScoreOverviewSection(score, currentLevel, skillsLevel, passStatus, date, time) {
        const levelDisplayName = getLevelDisplayName(currentLevel);
        const statusClass = passStatus.passed ? 'passed' : 'failed';
        const statusIcon = passStatus.passed ? '✅' : '❌';
        
        return `
            <div class="digital-skills-section digital-skills-score-overview">
                <h3>Assessment Overview</h3>
                <div class="digital-skills-overview-grid">
                    <div class="digital-skills-overview-card">
                        <div class="digital-skills-card-header">
                            <span class="digital-skills-card-icon">📊</span>
                            <span class="digital-skills-card-title">Overall Score</span>
                        </div>
                        <div class="digital-skills-card-value">${score}</div>
                        <div class="digital-skills-card-status ${statusClass}">
                            ${statusIcon} ${passStatus.status}
                        </div>
                    </div>
                    
                    <div class="digital-skills-overview-card">
                        <div class="digital-skills-card-header">
                            <span class="digital-skills-card-icon">🎯</span>
                            <span class="digital-skills-card-title">Assessment Level</span>
                        </div>
                        <div class="digital-skills-card-value">${levelDisplayName}</div>
                        <div class="digital-skills-card-subtitle">Completed Level</div>
                    </div>
                    
                    <div class="digital-skills-overview-card">
                        <div class="digital-skills-card-header">
                            <span class="digital-skills-card-icon">🤖</span>
                            <span class="digital-skills-card-title">AI Recommended Level</span>
                        </div>
                        <div class="digital-skills-card-value">${getLevelDisplayName(skillsLevel)}</div>
                        <div class="digital-skills-card-subtitle">Suggested Next Step</div>
                    </div>
                    
                    <div class="digital-skills-overview-card">
                        <div class="digital-skills-card-header">
                            <span class="digital-skills-card-icon">⏱️</span>
                            <span class="digital-skills-card-title">Time Spent</span>
                        </div>
                        <div class="digital-skills-card-value">${time}</div>
                        <div class="digital-skills-card-subtitle">Completed on ${date}</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create level progression section
     */
    function createLevelProgressionSection(data) {
        const levels = [
            'EntryLevel2', 'EntryLevel2Plus', 'EntryLevel3',
            'Level1', 'Level2', 'ICDLLevel2', 'ICDLLevel3'
        ];

        const progressItems = levels.map(level => {
            const levelKey = `digitalSkills${level}`;
            const levelData = data[levelKey];
            const isCompleted = levelData && levelData.completed;
            const isPassed = levelData && levelData.passed;
            const isCurrent = data.digitalSkillsCurrentLevel === level;

            let statusClass = 'not-attempted';
            let statusIcon = '⚪';
            let statusText = 'Not Attempted';

            if (isCompleted) {
                if (isPassed) {
                    statusClass = 'passed';
                    statusIcon = '✅';
                    statusText = `Passed (${levelData.score} points)`;
                } else {
                    statusClass = 'failed';
                    statusIcon = '❌';
                    statusText = `Failed (${levelData.score} points)`;
                }
            } else if (isCurrent) {
                statusClass = 'current';
                statusIcon = '🔄';
                statusText = 'Current Level';
            }

            return `
                <div class="digital-skills-level-item ${statusClass}">
                    <div class="digital-skills-level-icon">${statusIcon}</div>
                    <div class="digital-skills-level-info">
                        <div class="digital-skills-level-name">${getLevelDisplayName(level)}</div>
                        <div class="digital-skills-level-status">${statusText}</div>
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="digital-skills-section digital-skills-level-progression">
                <h3>Level Progression</h3>
                <div class="digital-skills-progression-container">
                    ${progressItems}
                </div>
            </div>
        `;
    }

    /**
     * Create AI analysis section
     */
    function createAIAnalysisSection(data) {
        const feedback = data.digitalSkillsFeedback || {};

        if (!feedback || Object.keys(feedback).length === 0) {
            return `
                <div class="digital-skills-section digital-skills-ai-analysis">
                    <h3>AI Analysis</h3>
                    <div class="digital-skills-no-data">
                        <p>AI analysis not available for this assessment.</p>
                    </div>
                </div>
            `;
        }

        const feedbackItems = Object.entries(feedback).map(([category, analysis]) => {
            const categoryName = formatCategoryName(category);
            return `
                <div class="digital-skills-feedback-item">
                    <div class="digital-skills-feedback-category">${categoryName}</div>
                    <div class="digital-skills-feedback-text">${analysis}</div>
                </div>
            `;
        }).join('');

        return `
            <div class="digital-skills-section digital-skills-ai-analysis">
                <h3>AI Analysis & Feedback</h3>
                <div class="digital-skills-feedback-container">
                    ${feedbackItems}
                </div>
            </div>
        `;
    }

    /**
     * Create course recommendation section
     */
    function createCourseRecommendationSection(data) {
        const courseRec = data.digitalSkillsCourseRecommendation;

        if (!courseRec) {
            return `
                <div class="digital-skills-section digital-skills-course-recommendation">
                    <h3>Course Recommendation</h3>
                    <div class="digital-skills-no-data">
                        <p>Course recommendations will be available after AI analysis.</p>
                    </div>
                </div>
            `;
        }

        const prerequisites = courseRec.prerequisites && courseRec.prerequisites.length > 0 ?
            courseRec.prerequisites.map(prereq => `<li>${prereq}</li>`).join('') :
            '<li>No specific prerequisites</li>';

        const keySkills = courseRec.keySkills && courseRec.keySkills.length > 0 ?
            courseRec.keySkills.map(skill => `<li>${skill}</li>`).join('') :
            '<li>Skills to be determined</li>';

        return `
            <div class="digital-skills-section digital-skills-course-recommendation">
                <h3>Recommended Next Course</h3>
                <div class="digital-skills-course-card">
                    <div class="digital-skills-course-header">
                        <div class="digital-skills-course-title">${courseRec.courseName || 'Course recommendation pending'}</div>
                        <div class="digital-skills-course-level">${courseRec.level || 'Level TBD'}</div>
                    </div>
                    <div class="digital-skills-course-description">
                        ${courseRec.description || 'Course description will be provided after analysis.'}
                    </div>
                    <div class="digital-skills-course-details">
                        <div class="digital-skills-course-duration">
                            <strong>Estimated Duration:</strong> ${courseRec.estimatedDuration || 'To be determined'}
                        </div>
                        <div class="digital-skills-course-prerequisites">
                            <strong>Prerequisites:</strong>
                            <ul>${prerequisites}</ul>
                        </div>
                        <div class="digital-skills-course-skills">
                            <strong>Key Skills Covered:</strong>
                            <ul>${keySkills}</ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create strengths and improvements section
     */
    function createStrengthsImprovementsSection(data) {
        const strengths = data.digitalSkillsStrengths || [];
        const improvements = data.digitalSkillsImprovements || [];

        const strengthsItems = strengths.length > 0 ?
            strengths.map(strength => `<li class="digital-skills-strength-item">✅ ${strength}</li>`).join('') :
            '<li class="digital-skills-no-data-item">Strengths analysis pending</li>';

        const improvementsItems = improvements.length > 0 ?
            improvements.map(improvement => `<li class="digital-skills-improvement-item">📈 ${improvement}</li>`).join('') :
            '<li class="digital-skills-no-data-item">Improvement areas analysis pending</li>';

        return `
            <div class="digital-skills-section digital-skills-strengths-improvements">
                <h3>Strengths & Areas for Improvement</h3>
                <div class="digital-skills-strengths-improvements-grid">
                    <div class="digital-skills-strengths-container">
                        <h4 class="digital-skills-strengths-title">Identified Strengths</h4>
                        <ul class="digital-skills-strengths-list">
                            ${strengthsItems}
                        </ul>
                    </div>
                    <div class="digital-skills-improvements-container">
                        <h4 class="digital-skills-improvements-title">Areas for Development</h4>
                        <ul class="digital-skills-improvements-list">
                            ${improvementsItems}
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create time analytics section
     */
    function createTimeAnalyticsSection(totalTimeSpent, completedAt) {
        const formattedTime = formatTime(totalTimeSpent);
        const completionDate = completedAt ?
            (completedAt.toDate ? completedAt.toDate() : new Date(completedAt)) :
            null;

        const timePerQuestion = totalTimeSpent > 0 ? Math.round(totalTimeSpent / 20) : 0; // Assuming average 20 questions

        return `
            <div class="digital-skills-section digital-skills-time-analytics">
                <h3>Time Analytics</h3>
                <div class="digital-skills-time-grid">
                    <div class="digital-skills-time-card">
                        <div class="digital-skills-time-icon">⏱️</div>
                        <div class="digital-skills-time-value">${formattedTime}</div>
                        <div class="digital-skills-time-label">Total Time Spent</div>
                    </div>
                    <div class="digital-skills-time-card">
                        <div class="digital-skills-time-icon">⚡</div>
                        <div class="digital-skills-time-value">${timePerQuestion}s</div>
                        <div class="digital-skills-time-label">Avg. per Question</div>
                    </div>
                    <div class="digital-skills-time-card">
                        <div class="digital-skills-time-icon">📅</div>
                        <div class="digital-skills-time-value">${completionDate ? completionDate.toLocaleDateString() : 'N/A'}</div>
                        <div class="digital-skills-time-label">Completion Date</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create next steps section
     */
    function createNextStepsSection(skillsLevel, passStatus) {
        const nextSteps = generateNextSteps(skillsLevel, passStatus);

        const stepsItems = nextSteps.map((step, index) => `
            <div class="digital-skills-next-step">
                <div class="digital-skills-step-number">${index + 1}</div>
                <div class="digital-skills-step-content">${step}</div>
            </div>
        `).join('');

        return `
            <div class="digital-skills-section digital-skills-next-steps">
                <h3>Recommended Next Steps</h3>
                <div class="digital-skills-steps-container">
                    ${stepsItems}
                </div>
            </div>
        `;
    }

    /**
     * Initialize event listeners
     */
    function initializeEventListeners(overlay) {
        // Close modal events
        const closeButton = overlay.querySelector('#close-digital-skills-modal');
        if (closeButton) {
            closeButton.addEventListener('click', hideModal);
        }

        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                hideModal();
            }
        });

        // Export data event
        const exportButton = overlay.querySelector('#export-digital-skills-data');
        if (exportButton) {
            exportButton.addEventListener('click', exportDigitalSkillsData);
        }

        // Escape key to close
        document.addEventListener('keydown', handleEscapeKey);
    }

    /**
     * Hide modal
     */
    function hideModal() {
        const overlay = document.getElementById('digital-skills-results-overlay');
        if (!overlay) return;

        const modalContent = overlay.querySelector('.digital-skills-modal-content');
        if (modalContent) {
            modalContent.style.transform = 'scale(0.95)';
            modalContent.style.opacity = '0';
        }

        overlay.style.opacity = '0';

        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);

        document.removeEventListener('keydown', handleEscapeKey);
    }

    /**
     * Handle escape key press
     */
    function handleEscapeKey(e) {
        if (e.key === 'Escape') {
            hideModal();
        }
    }

    /**
     * Export digital skills data
     */
    function exportDigitalSkillsData() {
        if (!currentDigitalSkillsData) return;

        try {
            const exportData = {
                userEmail: currentDigitalSkillsData.userEmail || 'Unknown',
                assessmentCompleted: currentDigitalSkillsData.digitalSkillsAssessmentCompleted,
                currentLevel: currentDigitalSkillsData.digitalSkillsCurrentLevel,
                overallScore: currentDigitalSkillsData.digitalSkillsOverallScore,
                skillsLevel: currentDigitalSkillsData.digitalSkillsSkillsLevel,
                highestLevelCompleted: currentDigitalSkillsData.digitalSkillsHighestLevelCompleted,
                completedAt: currentDigitalSkillsData.digitalSkillsAssessmentTimestamp,
                totalTimeSpent: currentDigitalSkillsData.totalTimeSpentOnDigitalSkills,
                feedback: currentDigitalSkillsData.digitalSkillsFeedback,
                strengths: currentDigitalSkillsData.digitalSkillsStrengths,
                improvements: currentDigitalSkillsData.digitalSkillsImprovements,
                courseRecommendation: currentDigitalSkillsData.digitalSkillsCourseRecommendation,
                confidenceAnalysis: currentDigitalSkillsData.digitalSkillsConfidenceAnalysis,
                exportedAt: new Date().toISOString()
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `digital-skills-assessment-${exportData.userEmail}-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            if (typeof showSuccessMessage === 'function') {
                showSuccessMessage('Digital skills assessment data exported successfully');
            }

        } catch (error) {
            console.error('Error exporting digital skills data:', error);
            if (typeof showErrorMessage === 'function') {
                showErrorMessage('Failed to export digital skills assessment data');
            }
        }
    }

    // Utility Functions

    /**
     * Get display name for assessment level
     */
    function getLevelDisplayName(level) {
        const displayNames = {
            'EntryLevel2': 'Computer Skills Beginners',
            'EntryLevel2Plus': 'Computer Skills Beginners Plus',
            'EntryLevel3': 'Improvers Plus',
            'Level1': 'Computer Skills for Everyday Life',
            'Level2': 'Computer Skills for Work',
            'ICDLLevel2': 'ICDL Level 2',
            'ICDLLevel3': 'ICDL Level 3'
        };
        return displayNames[level] || level;
    }

    /**
     * Format time in seconds to readable format
     */
    function formatTime(seconds) {
        if (!seconds || seconds === 0) return '0m 0s';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m ${remainingSeconds}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${remainingSeconds}s`;
        } else {
            return `${remainingSeconds}s`;
        }
    }

    /**
     * Determine pass status based on score and level
     */
    function determinePassStatus(score, level) {
        const passingScores = {
            'EntryLevel2': 12,
            'EntryLevel2Plus': 15,
            'EntryLevel3': 14,
            'Level1': 18,
            'Level2': 20,
            'ICDLLevel2': 22,
            'ICDLLevel3': 26
        };

        const requiredScore = passingScores[level] || 0;
        const passed = score >= requiredScore;

        return {
            passed: passed,
            status: passed ? 'Passed' : 'Failed',
            requiredScore: requiredScore,
            actualScore: score
        };
    }

    /**
     * Format category names for display
     */
    function formatCategoryName(category) {
        const categoryNames = {
            'basicComputerSkills': 'Basic Computer Skills',
            'internetAndEmail': 'Internet & Email',
            'digitalSafety': 'Digital Safety',
            'softwareApplications': 'Software Applications',
            'troubleshooting': 'Troubleshooting',
            'overall': 'Overall Assessment'
        };
        return categoryNames[category] || category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    /**
     * Generate next steps based on skills level and pass status
     */
    function generateNextSteps(skillsLevel, passStatus) {
        const steps = [];

        if (passStatus.passed) {
            steps.push('🎉 Congratulations on passing your digital skills assessment!');

            switch (skillsLevel) {
                case 'EntryLevel2':
                    steps.push('Consider enrolling in Computer Skills Beginners Plus to build on your foundation');
                    steps.push('Practice basic file management and computer navigation skills');
                    break;
                case 'EntryLevel3':
                    steps.push('You\'re ready for Computer Skills for Everyday Life Level 1');
                    steps.push('Focus on developing internet safety and email communication skills');
                    break;
                case 'Level1':
                    steps.push('Advance to Computer Skills for Work Level 2');
                    steps.push('Strengthen your Microsoft Office application skills');
                    break;
                case 'Level2':
                    steps.push('Consider ICDL Level 2 certification for professional development');
                    steps.push('Explore advanced workplace collaboration tools');
                    break;
                default:
                    steps.push('Continue building your digital skills with advanced courses');
                    steps.push('Consider specialized training in areas of interest');
            }
        } else {
            steps.push('📚 Review the areas for improvement identified in your assessment');
            steps.push('Practice the skills covered in your current level before retaking');
            steps.push('Consider additional study materials or tutorials for challenging topics');
            steps.push('Retake the assessment when you feel more confident');
        }

        steps.push('💬 Discuss your results with your instructor or career advisor');
        steps.push('📈 Track your progress and celebrate your achievements');

        return steps;
    }

})();
