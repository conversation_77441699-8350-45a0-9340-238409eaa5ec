# Digital Skills Assessment Database Schema Documentation

## Overview
This document provides comprehensive database schema documentation for the digital skills assessment system, specifically designed for admin dashboard requirements. It covers all field definitions, query patterns, and data structures needed for detailed reporting, analytics, and user management.

## Database Structure

### Collection Path
```
companies/{companyId}/users/{userEmail}
```
**Default Company**: `Birmingham` (for student-focused version)

## Complete Field Definitions

### 1. Assessment Metadata (Core Fields)

These fields track overall assessment completion and user information required for admin dashboards:

```javascript
{
  // User Identification
  userEmail: string,                              // Primary key - user's email address
  userCompany: string,                           // Always "Birmingham" for student users
  name: string,                                  // User's full name (optional)
  firstName: string,                             // User's first name (optional)
  lastName: string,                              // User's last name (optional)

  // Assessment Completion Status
  digitalSkillsAssessmentCompleted: boolean,     // true when any level completed
  digitalSkillsCurrentLevel: string,             // Last attempted level: "EntryLevel2", "EntryLevel2Plus", "Level1", "Level2", "EntryLevel3", "ICDLLevel2", "ICDLLevel3"
  digitalSkillsOverallScore: number,             // Score from most recent assessment (0-60 depending on level)
  digitalSkillsHighestLevelCompleted: string,    // Highest level successfully passed
  digitalSkillsAssessmentTimestamp: Timestamp,   // When assessment was last completed
  totalTimeSpentOnDigitalSkills: number,        // Total time in seconds across all attempts
  updatedAt: Timestamp                          // Last document update timestamp
}
```

### 2. AI Analysis Results (Admin Dashboard Critical)

These fields contain AI-generated analysis and recommendations essential for detailed admin reports:

```javascript
{
  // AI-Recommended Skills Level (Key for Course Placement)
  digitalSkillsSkillsLevel: string,              // AI-determined skill level: "EntryLevel2", "EntryLevel3", "Level1", "Level2", "Level3"

  // Comprehensive AI Feedback (Object with topic-specific analysis)
  digitalSkillsFeedback: {
    basicComputerSkills: string,                 // "Shows good understanding of fundamental computer operations..."
    internetAndEmail: string,                    // "Demonstrates competent email and web browsing skills..."
    digitalSafety: string,                       // "Awareness of basic security practices with room for improvement..."
    softwareApplications: string,                // "Comfortable with common productivity applications..."
    troubleshooting: string,                     // "Developing problem-solving skills for technical issues..."
    overall: string                              // "Overall performance indicates readiness for Level 2 coursework..."
  },

  // Strengths Array (For positive feedback display)
  digitalSkillsStrengths: [
    "Strong foundation in basic computer operations",
    "Good understanding of file management",
    "Confident with email communication"
  ],

  // Areas for Improvement Array (For development recommendations)
  digitalSkillsImprovements: [
    "Enhance digital safety awareness",
    "Develop advanced spreadsheet skills",
    "Improve troubleshooting techniques"
  ],

  // Course Recommendation Object (Critical for learning path guidance)
  digitalSkillsCourseRecommendation: {
    level: string,                               // "Level1", "Level2", etc.
    courseName: string,                          // "Computer Skills for Everyday Life Level 1"
    description: string,                         // "Based on your assessment results, we recommend..."
    prerequisites: [string],                     // ["Complete Entry Level 2", "Basic computer literacy"]
    estimatedDuration: string,                   // "6-8 weeks"
    keySkills: [string]                         // ["Microsoft Office", "Internet Safety", "Email Management"]
  },

  // Confidence Analysis Object (Self-assessment vs performance analysis)
  digitalSkillsConfidenceAnalysis: {
    overallConfidence: string,                   // "High", "Medium", "Low"
    confidenceAreas: [string],                   // Areas where user shows high confidence
    developmentAreas: [string],                  // Areas needing confidence building
    practicalReadiness: string                   // "Ready", "Developing", "Needs Support"
  }
}
```

### 3. Level-Specific Assessment Data

Each completed level stores detailed performance data. **Note**: Only the most recently completed level is stored in the main document. Historical data requires separate collection if needed.

```javascript
{
  // Entry Level 2 - Computer Skills Beginners (15 questions, 25 min, pass: 12/30)
  digitalSkillsEntryLevel2: {
    completed: boolean,                          // true when level completed
    score: number,                              // Points earned (0-30)
    passed: boolean,                            // true if score >= 12
    timeSpent: number,                          // Time in seconds
    completedAt: Timestamp,                     // Completion timestamp
    responses: [                                // Array of user responses
      {
        questionId: number,                     // Question identifier (1-15)
        answer: string,                         // User's selected answer
        isCorrect: boolean,                     // Whether answer was correct
        timeSpent: number,                      // Time spent on this question (seconds)
        questionType: string                    // "multiple-choice", "interactive-rating", "descriptive-confidence"
      }
    ],
    topicBreakdown: {                          // Performance analysis by topic
      computerBasics: { correct: number, total: number, percentage: number },
      mouseKeyboard: { correct: number, total: number, percentage: number },
      basicOperations: { correct: number, total: number, percentage: number },
      fileManagement: { correct: number, total: number, percentage: number },
      basicSafety: { correct: number, total: number, percentage: number }
    }
  },

  // Entry Level 2/3 - Computer Skills Beginners Plus (18 questions, 30 min, pass: 15/36)
  digitalSkillsEntryLevel2Plus: {
    completed: boolean,
    score: number,                              // Points earned (0-36)
    passed: boolean,                            // true if score >= 15
    timeSpent: number,
    completedAt: Timestamp,
    responses: [...],                           // Same structure as above
    topicBreakdown: {
      laptopDesktop: { correct: number, total: number, percentage: number },
      applications: { correct: number, total: number, percentage: number },
      internetSafety: { correct: number, total: number, percentage: number },
      emailBasics: { correct: number, total: number, percentage: number },
      digitalCitizenship: { correct: number, total: number, percentage: number }
    }
  },

  // Level 1 - Computer Skills for Everyday Life (20 questions, 35 min, pass: 18/40)
  digitalSkillsLevel1: {
    completed: boolean,
    score: number,                              // Points earned (0-40)
    passed: boolean,                            // true if score >= 18
    timeSpent: number,
    completedAt: Timestamp,
    responses: [...],                           // Same structure as above
    topicBreakdown: {
      microsoftApps: { correct: number, total: number, percentage: number },
      onlineBanking: { correct: number, total: number, percentage: number },
      cloudStorage: { correct: number, total: number, percentage: number },
      digitalIdentity: { correct: number, total: number, percentage: number },
      internetSkills: { correct: number, total: number, percentage: number }
    }
  },

  // Level 2 - Computer Skills for Work (22 questions, 40 min, pass: 20/44)
  digitalSkillsLevel2: {
    completed: boolean,
    score: number,                              // Points earned (0-44)
    passed: boolean,                            // true if score >= 20
    timeSpent: number,
    completedAt: Timestamp,
    responses: [...],                           // Same structure as above
    topicBreakdown: {
      advancedFormatting: { correct: number, total: number, percentage: number },
      spreadsheets: { correct: number, total: number, percentage: number },
      presentations: { correct: number, total: number, percentage: number },
      workplaceSkills: { correct: number, total: number, percentage: number },
      collaboration: { correct: number, total: number, percentage: number }
    }
  },

  // Entry Level 3 - Improvers Plus (16 questions, 30 min, pass: 14/32)
  digitalSkillsEntryLevel3: {
    completed: boolean,
    score: number,                              // Points earned (0-32)
    passed: boolean,                            // true if score >= 14
    timeSpent: number,
    completedAt: Timestamp,
    responses: [...],                           // Same structure as above
    topicBreakdown: {
      operatingSystems: { correct: number, total: number, percentage: number },
      emailProficiency: { correct: number, total: number, percentage: number },
      onlineTransactions: { correct: number, total: number, percentage: number },
      digitalSafety: { correct: number, total: number, percentage: number },
      troubleshooting: { correct: number, total: number, percentage: number }
    }
  },

  // ICDL Level 2 (25 questions, 45 min, pass: 22/50)
  digitalSkillsICDLLevel2: {
    completed: boolean,
    score: number,                              // Points earned (0-50)
    passed: boolean,                            // true if score >= 22
    timeSpent: number,
    completedAt: Timestamp,
    responses: [...],                           // Same structure as above
    topicBreakdown: {
      timedExam: { correct: number, total: number, percentage: number },
      wordAdvanced: { correct: number, total: number, percentage: number },
      excelAdvanced: { correct: number, total: number, percentage: number },
      powerpointAdvanced: { correct: number, total: number, percentage: number },
      employmentSkills: { correct: number, total: number, percentage: number }
    }
  },

  // ICDL Level 3 (30 questions, 50 min, pass: 26/60)
  digitalSkillsICDLLevel3: {
    completed: boolean,
    score: number,                              // Points earned (0-60)
    passed: boolean,                            // true if score >= 26
    timeSpent: number,
    completedAt: Timestamp,
    responses: [...],                           // Same structure as above
    topicBreakdown: {
      advancedMicrosoft: { correct: number, total: number, percentage: number },
      itCareers: { correct: number, total: number, percentage: number },
      higherEducation: { correct: number, total: number, percentage: number },
      professionalSkills: { correct: number, total: number, percentage: number },
      certification: { correct: number, total: number, percentage: number }
    }
  }
}
```
```

### 4. Detailed Response Logging (Admin Quality Assurance)

**Optional but recommended for admin review and quality assurance**. Stores comprehensive interaction data:

```javascript
{
  // Detailed responses stored separately for admin analysis
  digitalSkillsDetailedResponses: {
    assessmentMetadata: {
      startTime: Timestamp,                      // Assessment start time
      endTime: Timestamp,                        // Assessment completion time
      userAgent: string,                         // Browser/device information
      sessionId: string,                         // Unique session identifier
      analysisTimestamp: Timestamp,              // When AI analysis was performed
      aiAnalysisUsed: boolean                    // Whether AI analysis was successful
    },

    // Complete question and response data for admin review
    questionResponses: [
      {
        questionId: number,                      // Question identifier (1-30 depending on level)
        questionText: string,                    // Full question text as presented
        questionType: string,                    // "multiple-choice", "interactive-rating", "descriptive-confidence"
        options: [string],                       // Available answer options
        correctAnswer: string,                   // Correct answer (for multiple-choice)
        userAnswer: string,                      // User's selected/entered answer
        isCorrect: boolean,                      // Whether answer was correct
        timeSpent: number,                       // Time spent on this question (seconds)
        timestamp: Timestamp,                    // When question was answered
        topic: string,                           // Question topic/category
        difficulty: string,                      // "easy", "medium", "hard"

        // Detailed interaction tracking
        interactionLogs: [
          {
            timestamp: Timestamp,
            action: string,                      // "question_viewed", "answer_selected", "answer_changed", "confidence_rating"
            questionId: number,
            data: {                              // Action-specific data
              selectedAnswer: string,            // For answer selection
              previousAnswer: string,            // For answer changes
              rating: number,                    // For confidence ratings
              scale: number                      // Rating scale (1-10)
            }
          }
        ]
      }
    ]
  }
}
```

## Admin Dashboard API Endpoints

### 1. Analytics Endpoint
**GET** `/api/admin/digital-skills-analytics`

**Query Parameters:**
- `company` (optional): Company ID, defaults to "Birmingham"

**Response Structure:**
```javascript
{
  success: true,
  data: {
    totalAssessments: 150,                       // Total completed assessments
    levelBreakdown: {                           // Performance by level
      EntryLevel2: {
        completed: 25,
        passed: 20,
        averageScore: 14,
        passRate: 80
      },
      EntryLevel2Plus: {
        completed: 30,
        passed: 25,
        averageScore: 16,
        passRate: 83
      },
      EntryLevel3: {
        completed: 35,
        passed: 28,
        averageScore: 18,
        passRate: 80
      },
      Level1: {
        completed: 25,
        passed: 22,
        averageScore: 20,
        passRate: 88
      },
      Level2: {
        completed: 20,
        passed: 18,
        averageScore: 22,
        passRate: 90
      },
      ICDLLevel2: {
        completed: 10,
        passed: 8,
        averageScore: 24,
        passRate: 80
      },
      ICDLLevel3: {
        completed: 5,
        passed: 4,
        averageScore: 28,
        passRate: 80
      }
    },
    overallPassRate: 78,                        // Overall pass percentage
    averageTimeSpent: 1650,                     // Average time in seconds
    skillsLevelDistribution: {                  // AI-recommended skill levels
      "EntryLevel2": 15,
      "EntryLevel3": 45,
      "Level1": 60,
      "Level2": 25,
      "Level3": 5
    },
    confidenceAnalysis: {                       // Confidence vs performance analysis
      highConfidence: 45,                       // Users with high self-confidence
      mediumConfidence: 85,                     // Users with medium self-confidence
      lowConfidence: 20                         // Users with low self-confidence
    },
    topicPerformance: {                         // Performance across all topics
      computerBasics: { averageScore: 85, totalQuestions: 450 },
      internetSafety: { averageScore: 78, totalQuestions: 380 },
      microsoftApps: { averageScore: 72, totalQuestions: 320 }
    }
  }
}
```

### 2. User Responses Endpoint
**GET** `/api/admin/digital-skills-responses`

**Query Parameters:**
- `company` (optional): Company ID, defaults to "Birmingham"
- `limit` (optional): Number of results to return, defaults to 50
- `startAfter` (optional): For pagination

**Response Structure:**
```javascript
{
  success: true,
  data: [
    {
      userEmail: "<EMAIL>",
      name: "John Doe",
      currentLevel: "Level1",                    // Last attempted level
      overallScore: 18,                          // Score from most recent assessment
      skillsLevel: "Level2",                     // AI-recommended skill level
      completedAt: Timestamp,                    // When assessment was completed
      timeSpent: 1800,                          // Time spent in seconds
      feedback: {                               // Complete AI feedback object
        basicComputerSkills: "Shows good understanding...",
        internetAndEmail: "Demonstrates competent skills...",
        digitalSafety: "Awareness of basic security...",
        softwareApplications: "Comfortable with applications...",
        troubleshooting: "Developing problem-solving skills...",
        overall: "Overall performance indicates readiness..."
      },
      strengths: [                              // Array of identified strengths
        "Strong foundation in basic computer operations",
        "Good understanding of file management",
        "Confident with email communication"
      ],
      improvements: [                           // Array of improvement areas
        "Enhance digital safety awareness",
        "Develop advanced spreadsheet skills",
        "Improve troubleshooting techniques"
      ],
      courseRecommendation: {                   // Course recommendation object
        level: "Level2",
        courseName: "Computer Skills for Work Level 2",
        description: "Based on your assessment results...",
        prerequisites: ["Complete Level 1"],
        estimatedDuration: "8-10 weeks",
        keySkills: ["Advanced Excel", "PowerPoint", "Workplace Collaboration"]
      },
      confidenceAnalysis: {                     // Confidence analysis object
        overallConfidence: "Medium",
        confidenceAreas: ["Basic computer skills", "Email management"],
        developmentAreas: ["Advanced applications", "Troubleshooting"],
        practicalReadiness: "Developing"
      }
    }
    // ... more user responses
  ]
}
```

### 3. Individual User Response Endpoint
**GET** `/api/admin/digital-skills-responses/:email`

**Query Parameters:**
- `company` (optional): Company ID, defaults to "Birmingham"

**Response Structure:**
```javascript
{
  success: true,
  data: {
    userEmail: "<EMAIL>",
    name: "John Doe",
    assessmentCompleted: true,
    currentLevel: "Level1",                      // Last attempted level
    overallScore: 18,                           // Score from most recent assessment
    skillsLevel: "Level2",                      // AI-recommended skill level
    highestLevelCompleted: "Level1",            // Highest level successfully passed
    completedAt: Timestamp,                     // When assessment was completed
    totalTimeSpent: 1800,                      // Total time spent across all attempts

    // Complete AI analysis data
    feedback: { /* Complete feedback object */ },
    strengths: [ /* Array of strengths */ ],
    improvements: [ /* Array of improvements */ ],
    courseRecommendation: { /* Complete course recommendation */ },
    confidenceAnalysis: { /* Complete confidence analysis */ },

    // Detailed response data (if available)
    detailedResponses: { /* Complete interaction logs */ },

    // Level-specific data (only for completed levels)
    entryLevel2: { /* Level data if completed */ },
    entryLevel2Plus: { /* Level data if completed */ },
    entryLevel3: { /* Level data if completed */ },
    level1: { /* Level data if completed */ },
    level2: { /* Level data if completed */ },
    icdlLevel2: { /* Level data if completed */ },
    icdlLevel3: { /* Level data if completed */ }
  }
}
```
## Database Query Examples

### 1. Retrieve All Digital Skills Assessment Results
```javascript
// Get all users who completed digital skills assessments
const query = db.collection('companies')
  .doc('Birmingham')
  .collection('users')
  .where('digitalSkillsAssessmentCompleted', '==', true)
  .orderBy('digitalSkillsAssessmentTimestamp', 'desc');

const snapshot = await query.get();
const results = [];

snapshot.forEach(doc => {
  const data = doc.data();
  results.push({
    userEmail: doc.id,
    name: data.name || `${data.firstName || ''} ${data.lastName || ''}`.trim(),
    currentLevel: data.digitalSkillsCurrentLevel,
    overallScore: data.digitalSkillsOverallScore,
    skillsLevel: data.digitalSkillsSkillsLevel,
    completedAt: data.digitalSkillsAssessmentTimestamp,
    timeSpent: data.totalTimeSpentOnDigitalSkills,
    feedback: data.digitalSkillsFeedback,
    strengths: data.digitalSkillsStrengths,
    improvements: data.digitalSkillsImprovements,
    courseRecommendation: data.digitalSkillsCourseRecommendation,
    confidenceAnalysis: data.digitalSkillsConfidenceAnalysis
  });
});
```

### 2. Filter by Skill Level and Date Range
```javascript
// Get users with specific AI-recommended skill level from last 30 days
const thirtyDaysAgo = new Date();
thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

const query = db.collection('companies')
  .doc('Birmingham')
  .collection('users')
  .where('digitalSkillsSkillsLevel', '==', 'Level2')
  .where('digitalSkillsAssessmentTimestamp', '>=', thirtyDaysAgo)
  .orderBy('digitalSkillsAssessmentTimestamp', 'desc');
```

### 3. Aggregate Performance Data
```javascript
// Calculate level-specific analytics
const snapshot = await db.collection('companies')
  .doc('Birmingham')
  .collection('users')
  .where('digitalSkillsAssessmentCompleted', '==', true)
  .get();

const analytics = {
  totalAssessments: 0,
  levelBreakdown: {},
  skillsLevelDistribution: {},
  averageTimeSpent: 0,
  overallPassRate: 0
};

let totalTimeSpent = 0;
let totalPassed = 0;

snapshot.forEach(doc => {
  const data = doc.data();
  analytics.totalAssessments++;

  const currentLevel = data.digitalSkillsCurrentLevel;
  const overallScore = data.digitalSkillsOverallScore || 0;
  const timeSpent = data.totalTimeSpentOnDigitalSkills || 0;
  const skillsLevel = data.digitalSkillsSkillsLevel;

  // Level-specific data aggregation
  if (currentLevel && data[`digitalSkills${currentLevel}`]) {
    const levelData = data[`digitalSkills${currentLevel}`];
    if (!analytics.levelBreakdown[currentLevel]) {
      analytics.levelBreakdown[currentLevel] = {
        completed: 0,
        passed: 0,
        totalScore: 0,
        averageScore: 0
      };
    }

    analytics.levelBreakdown[currentLevel].completed++;
    analytics.levelBreakdown[currentLevel].totalScore += overallScore;

    if (levelData.passed) {
      analytics.levelBreakdown[currentLevel].passed++;
      totalPassed++;
    }
  }

  // Skills level distribution
  if (skillsLevel) {
    analytics.skillsLevelDistribution[skillsLevel] =
      (analytics.skillsLevelDistribution[skillsLevel] || 0) + 1;
  }

  totalTimeSpent += timeSpent;
});

// Calculate averages
analytics.averageTimeSpent = Math.round(totalTimeSpent / analytics.totalAssessments);
analytics.overallPassRate = Math.round((totalPassed / analytics.totalAssessments) * 100);

// Calculate level-specific averages
Object.keys(analytics.levelBreakdown).forEach(level => {
  const levelData = analytics.levelBreakdown[level];
  levelData.averageScore = Math.round(levelData.totalScore / levelData.completed);
  levelData.passRate = Math.round((levelData.passed / levelData.completed) * 100);
});
```
## Data Structure Specifications

### 1. JSON Field Formats for Complex Data

#### AI Analysis Results Format
```javascript
// digitalSkillsFeedback - Object with topic-specific feedback
{
  basicComputerSkills: "Shows good understanding of fundamental computer operations and demonstrates confidence with basic tasks like file management and navigation.",
  internetAndEmail: "Demonstrates competent email and web browsing skills with good awareness of online etiquette and communication practices.",
  digitalSafety: "Awareness of basic security practices with room for improvement in advanced threat recognition and password management.",
  softwareApplications: "Comfortable with common productivity applications but would benefit from advanced feature training.",
  troubleshooting: "Developing problem-solving skills for technical issues with good logical approach to basic problems.",
  overall: "Overall performance indicates readiness for Level 2 coursework with strong foundational skills and positive learning attitude."
}

// digitalSkillsStrengths - Array of positive performance indicators
[
  "Strong foundation in basic computer operations",
  "Good understanding of file management and organization",
  "Confident with email communication and etiquette",
  "Shows initiative in learning new digital tools",
  "Demonstrates good problem-solving approach"
]

// digitalSkillsImprovements - Array of development areas
[
  "Enhance digital safety awareness and password security",
  "Develop advanced spreadsheet formulas and functions",
  "Improve troubleshooting techniques for complex issues",
  "Strengthen understanding of cloud storage best practices",
  "Build confidence with presentation software features"
]

// digitalSkillsCourseRecommendation - Structured course guidance
{
  level: "Level2",
  courseName: "Computer Skills for Work Level 2",
  description: "Based on your assessment results, we recommend advancing to Level 2 which focuses on workplace digital skills including advanced Microsoft Office applications, professional communication, and collaborative tools.",
  prerequisites: [
    "Complete Computer Skills for Everyday Life Level 1",
    "Basic proficiency in Microsoft Office applications",
    "Understanding of internet safety principles"
  ],
  estimatedDuration: "8-10 weeks",
  keySkills: [
    "Advanced Excel formulas and data analysis",
    "Professional PowerPoint presentations",
    "Collaborative document editing",
    "Workplace communication tools",
    "Project management basics"
  ],
  nextSteps: [
    "Enroll in Level 2 course",
    "Practice advanced Excel functions",
    "Review workplace digital communication standards"
  ]
}

// digitalSkillsConfidenceAnalysis - Self-assessment vs performance analysis
{
  overallConfidence: "Medium",
  confidenceAreas: [
    "Basic computer operations",
    "Email management and communication",
    "File organization and storage",
    "Internet browsing and research"
  ],
  developmentAreas: [
    "Advanced spreadsheet functions",
    "Presentation design and delivery",
    "Troubleshooting complex technical issues",
    "Digital security and privacy settings"
  ],
  practicalReadiness: "Developing",
  confidenceVsPerformance: {
    overconfident: ["Digital safety practices"],
    underconfident: ["Spreadsheet capabilities", "Presentation skills"],
    wellCalibrated: ["Basic computer operations", "Email communication"]
  }
}
```

### 2. Relationship Mappings

#### User Profile to Assessment Results
```javascript
// Primary relationship: companies/{companyId}/users/{userEmail}
{
  // User identification
  userEmail: "<EMAIL>",
  userCompany: "Birmingham",

  // Links to assessment results
  digitalSkillsAssessmentCompleted: true,
  digitalSkillsCurrentLevel: "Level1",
  digitalSkillsHighestLevelCompleted: "Level1",

  // Cross-references to other assessments
  englishAssessmentCompleted: true,
  englishProficiencyLevel: "Level2",
  mathAssessmentCompleted: false,

  // Unified user profile
  name: "John Doe",
  firstName: "John",
  lastName: "Doe",
  createdAt: Timestamp,
  updatedAt: Timestamp
}
```

#### Assessment Level Hierarchy
```javascript
// Level progression mapping
const levelHierarchy = {
  "EntryLevel2": {
    order: 1,
    name: "Computer Skills Beginners",
    prerequisite: null,
    nextLevel: "EntryLevel2Plus"
  },
  "EntryLevel2Plus": {
    order: 2,
    name: "Computer Skills Beginners Plus",
    prerequisite: "EntryLevel2",
    nextLevel: "EntryLevel3"
  },
  "EntryLevel3": {
    order: 3,
    name: "Improvers Plus",
    prerequisite: "EntryLevel2Plus",
    nextLevel: "Level1"
  },
  "Level1": {
    order: 4,
    name: "Computer Skills for Everyday Life",
    prerequisite: "EntryLevel3",
    nextLevel: "Level2"
  },
  "Level2": {
    order: 5,
    name: "Computer Skills for Work",
    prerequisite: "Level1",
    nextLevel: "ICDLLevel2"
  },
  "ICDLLevel2": {
    order: 6,
    name: "ICDL Level 2",
    prerequisite: "Level2",
    nextLevel: "ICDLLevel3"
  },
  "ICDLLevel3": {
    order: 7,
    name: "ICDL Level 3",
    prerequisite: "ICDLLevel2",
    nextLevel: null
  }
};
```
### 3. Index Recommendations for Optimal Query Performance

```javascript
// Firestore composite indexes for efficient queries
const recommendedIndexes = [
  // Basic assessment status queries
  {
    collection: "users",
    fields: [
      { fieldPath: "digitalSkillsAssessmentCompleted", order: "ASCENDING" },
      { fieldPath: "digitalSkillsAssessmentTimestamp", order: "DESCENDING" }
    ]
  },

  // Skill level filtering with date range
  {
    collection: "users",
    fields: [
      { fieldPath: "digitalSkillsSkillsLevel", order: "ASCENDING" },
      { fieldPath: "digitalSkillsAssessmentTimestamp", order: "DESCENDING" }
    ]
  },

  // Level-specific performance queries
  {
    collection: "users",
    fields: [
      { fieldPath: "digitalSkillsCurrentLevel", order: "ASCENDING" },
      { fieldPath: "digitalSkillsOverallScore", order: "DESCENDING" }
    ]
  },

  // Admin dashboard analytics
  {
    collection: "users",
    fields: [
      { fieldPath: "digitalSkillsAssessmentCompleted", order: "ASCENDING" },
      { fieldPath: "digitalSkillsCurrentLevel", order: "ASCENDING" },
      { fieldPath: "digitalSkillsAssessmentTimestamp", order: "DESCENDING" }
    ]
  }
];
```

## Frontend Integration Guidance

### 1. Sample API Endpoint Structures

#### Assessment Results Dashboard Component
```javascript
// Frontend component data fetching
class DigitalSkillsDashboard {
  async loadAnalytics() {
    try {
      const response = await fetch('/api/admin/digital-skills-analytics?company=Birmingham');
      const { data } = await response.json();

      this.setState({
        totalAssessments: data.totalAssessments,
        levelBreakdown: data.levelBreakdown,
        skillsDistribution: data.skillsLevelDistribution,
        overallPassRate: data.overallPassRate,
        averageTimeSpent: this.formatTime(data.averageTimeSpent),
        confidenceAnalysis: data.confidenceAnalysis
      });
    } catch (error) {
      console.error('Failed to load analytics:', error);
    }
  }

  async loadUserResponses(page = 1, limit = 50) {
    try {
      const response = await fetch(`/api/admin/digital-skills-responses?company=Birmingham&limit=${limit}&page=${page}`);
      const { data } = await response.json();

      return data.map(user => ({
        email: user.userEmail,
        name: user.name,
        level: user.currentLevel,
        score: user.overallScore,
        skillsLevel: user.skillsLevel,
        completedAt: new Date(user.completedAt.seconds * 1000),
        timeSpent: this.formatTime(user.timeSpent),
        passed: this.calculatePassStatus(user.overallScore, user.currentLevel)
      }));
    } catch (error) {
      console.error('Failed to load user responses:', error);
      return [];
    }
  }

  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }

  calculatePassStatus(score, level) {
    const passingScores = {
      'EntryLevel2': 12,
      'EntryLevel2Plus': 15,
      'EntryLevel3': 14,
      'Level1': 18,
      'Level2': 20,
      'ICDLLevel2': 22,
      'ICDLLevel3': 26
    };
    return score >= (passingScores[level] || 0);
  }
}
```

### 2. Expected Response Formats for Dashboard Components

#### Level Performance Chart Component
```javascript
// Data transformation for chart visualization
const transformLevelData = (levelBreakdown) => {
  return Object.entries(levelBreakdown).map(([level, data]) => ({
    level: level,
    name: getLevelDisplayName(level),
    completed: data.completed,
    passed: data.passed,
    passRate: data.passRate,
    averageScore: data.averageScore,
    color: getLevelColor(level)
  }));
};

const getLevelDisplayName = (level) => {
  const displayNames = {
    'EntryLevel2': 'Computer Skills Beginners',
    'EntryLevel2Plus': 'Computer Skills Beginners Plus',
    'EntryLevel3': 'Improvers Plus',
    'Level1': 'Computer Skills for Everyday Life',
    'Level2': 'Computer Skills for Work',
    'ICDLLevel2': 'ICDL Level 2',
    'ICDLLevel3': 'ICDL Level 3'
  };
  return displayNames[level] || level;
};
```

#### Skills Distribution Pie Chart
```javascript
// Data transformation for skills level distribution
const transformSkillsDistribution = (skillsLevelDistribution) => {
  const total = Object.values(skillsLevelDistribution).reduce((sum, count) => sum + count, 0);

  return Object.entries(skillsLevelDistribution).map(([level, count]) => ({
    level: level,
    name: getLevelDisplayName(level),
    count: count,
    percentage: Math.round((count / total) * 100),
    color: getSkillsLevelColor(level)
  }));
};
```

#### User Detail Modal Component
```javascript
// Individual user assessment detail view
class UserDetailModal {
  async loadUserDetail(userEmail) {
    try {
      const response = await fetch(`/api/admin/digital-skills-responses/${encodeURIComponent(userEmail)}?company=Birmingham`);
      const { data } = await response.json();

      return {
        userInfo: {
          email: data.userEmail,
          name: data.name,
          completedAt: new Date(data.completedAt.seconds * 1000),
          totalTimeSpent: this.formatTime(data.totalTimeSpent)
        },
        assessmentResults: {
          currentLevel: data.currentLevel,
          overallScore: data.overallScore,
          skillsLevel: data.skillsLevel,
          highestLevelCompleted: data.highestLevelCompleted
        },
        aiAnalysis: {
          feedback: data.feedback,
          strengths: data.strengths,
          improvements: data.improvements,
          courseRecommendation: data.courseRecommendation,
          confidenceAnalysis: data.confidenceAnalysis
        },
        levelSpecificData: this.extractLevelData(data)
      };
    } catch (error) {
      console.error('Failed to load user detail:', error);
      return null;
    }
  }

  extractLevelData(data) {
    const levels = ['entryLevel2', 'entryLevel2Plus', 'entryLevel3', 'level1', 'level2', 'icdlLevel2', 'icdlLevel3'];
    const levelData = {};

    levels.forEach(level => {
      if (data[level] && data[level].completed) {
        levelData[level] = {
          score: data[level].score,
          passed: data[level].passed,
          timeSpent: data[level].timeSpent,
          completedAt: new Date(data[level].completedAt.seconds * 1000),
          topicBreakdown: data[level].topicBreakdown,
          responses: data[level].responses
        };
      }
    });

    return levelData;
  }
}
```

### 3. Data Transformation Examples for Visualization Components

#### Topic Performance Radar Chart
```javascript
// Transform topic breakdown data for radar chart visualization
const transformTopicPerformance = (topicBreakdown) => {
  return Object.entries(topicBreakdown).map(([topic, performance]) => ({
    topic: formatTopicName(topic),
    score: performance.percentage,
    correct: performance.correct,
    total: performance.total,
    color: getTopicColor(topic)
  }));
};

const formatTopicName = (topic) => {
  const topicNames = {
    'computerBasics': 'Computer Basics',
    'mouseKeyboard': 'Mouse & Keyboard',
    'basicOperations': 'Basic Operations',
    'fileManagement': 'File Management',
    'basicSafety': 'Basic Safety',
    'internetSafety': 'Internet Safety',
    'microsoftApps': 'Microsoft Applications',
    'onlineBanking': 'Online Banking',
    'cloudStorage': 'Cloud Storage',
    'digitalIdentity': 'Digital Identity',
    'advancedFormatting': 'Advanced Formatting',
    'spreadsheets': 'Spreadsheets',
    'presentations': 'Presentations',
    'workplaceSkills': 'Workplace Skills',
    'collaboration': 'Collaboration'
  };
  return topicNames[topic] || topic;
};
```

#### Progress Indicator Component
```javascript
// Transform assessment progress for progress indicators
const transformProgressData = (userData) => {
  const allLevels = ['EntryLevel2', 'EntryLevel2Plus', 'EntryLevel3', 'Level1', 'Level2', 'ICDLLevel2', 'ICDLLevel3'];

  return allLevels.map(level => {
    const levelKey = `digitalSkills${level}`;
    const levelData = userData[levelKey];

    return {
      level: level,
      name: getLevelDisplayName(level),
      status: levelData ? (levelData.completed ? (levelData.passed ? 'passed' : 'failed') : 'in-progress') : 'not-started',
      score: levelData ? levelData.score : null,
      timeSpent: levelData ? levelData.timeSpent : null,
      completedAt: levelData ? levelData.completedAt : null
    };
  });
};
```

## Integration with Existing System

### Database Compatibility
- **Consistent Structure**: Uses same `companies/{companyId}/users/{userEmail}` pattern as English and Mathematics assessments
- **Flattened Schema**: All digital skills fields stored at document root level for efficient querying
- **Unified User Profile**: Integrates seamlessly with existing user identification and company assignment
- **Cross-Assessment References**: Links to English and Mathematics assessment completion status

### Document Size Optimization
- **Core Fields**: ~3-5KB per user (metadata, scores, AI analysis)
- **Level-Specific Data**: ~2-4KB per completed level
- **Detailed Responses**: ~8-15KB (optional, for admin quality assurance)
- **Total Estimated Size**: 13-24KB per user with full data
- **Firestore Limit**: Well within 1MB document size limit

### Performance Considerations
- **Indexed Queries**: All admin dashboard queries use recommended composite indexes
- **Batch Operations**: Analytics calculations use efficient batch processing
- **Caching Strategy**: Frequently accessed data cached at application level
- **Pagination Support**: Large result sets paginated to maintain performance

This comprehensive schema documentation provides all necessary information for admin dashboard development, database optimization, and frontend integration while maintaining compatibility with the existing assessment platform architecture.
