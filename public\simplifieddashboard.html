<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simplified Admin Dashboard - Skills Assess</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="user-menu.css">
    
    <style>
        /* Custom styles for simplified dashboard */
        .simplified-dashboard {
            font-family: 'Montserrat', sans-serif;
            background: url('BG.png') center center/cover no-repeat fixed;
            min-height: 100vh;
            position: relative;
        }

        .simplified-dashboard::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            z-index: -1;
        }

        .dashboard-container {
            width: 100%;
            margin: 0;
            padding: 2rem;
            overflow: hidden; /* Prevent horizontal scroll on main container */
        }

        .dashboard-header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .dashboard-header:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.08);
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1e3a8a;
            margin-bottom: 0.5rem;
            transition: color 0.3s ease;
        }

        .dashboard-subtitle {
            color: #64748b;
            font-size: 1rem;
            line-height: 1.5;
        }

        /* Summary Cards Styles */
        .summary-cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
            background: rgba(0, 0, 0, 0.02);
            backdrop-filter: blur(10px);
            padding: 1rem;
            border-radius: 16px;
            margin-top: 1rem;
        }

        .summary-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .summary-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.08);
        }

        .summary-card-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .summary-card-icon svg {
            width: 1.5rem;
            height: 1.5rem;
        }

        #total-students-card .summary-card-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
        }

        #english-completed-card .summary-card-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        #math-completed-card .summary-card-icon {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        #overall-completion-card .summary-card-icon {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
        }

        .summary-card-content {
            flex: 1;
            min-width: 0;
        }

        .summary-card-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1e3a8a;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .summary-card-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 0.25rem;
        }

        .summary-card-percentage {
            font-size: 0.75rem;
            font-weight: 600;
            color: #10b981;
        }

        .summary-card-sublabel {
            font-size: 0.75rem;
            color: #94a3b8;
            font-style: italic;
        }

        /* Summary Card Skeleton Loaders */
        .summary-card-skeleton {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .skeleton-card-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 12px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            flex-shrink: 0;
        }

        .skeleton-card-content {
            flex: 1;
            min-width: 0;
        }

        .skeleton-card-value {
            height: 2rem;
            width: 4rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 6px;
            margin-bottom: 0.5rem;
        }

        .skeleton-card-label {
            height: 0.875rem;
            width: 6rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 0.25rem;
        }

        .skeleton-card-percentage {
            height: 0.75rem;
            width: 3rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        /* Hide real cards during loading */
        .summary-cards-container.loading .summary-card {
            display: none;
        }

        .summary-cards-container.loading .summary-card-skeleton {
            display: flex;
        }

        .summary-cards-container:not(.loading) .summary-card-skeleton {
            display: none;
        }

        /* Summary Card Skeleton Loaders */
        .summary-card-skeleton {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .skeleton-card-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 12px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            flex-shrink: 0;
        }

        .skeleton-card-content {
            flex: 1;
            min-width: 0;
        }

        .skeleton-card-value {
            height: 2rem;
            width: 4rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 6px;
            margin-bottom: 0.5rem;
        }

        .skeleton-card-label {
            height: 0.875rem;
            width: 6rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 0.25rem;
        }

        .skeleton-card-percentage {
            height: 0.75rem;
            width: 3rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        /* Hide real cards during loading */
        .summary-cards-container.loading .summary-card {
            display: none;
        }

        .summary-cards-container.loading .summary-card-skeleton {
            display: flex;
        }

        .summary-cards-container:not(.loading) .summary-card-skeleton {
            display: none;
        }
        
        .students-table-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 400px); /* Fixed height accounting for header and summary cards */
            min-height: 600px;
            max-height: 800px;
            position: relative;
            z-index: 1; /* Low z-index to stay below user menu */
        }

        .students-table-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.08);
        }

        /* Ensure user menu interactions are not blocked */
        .students-table-container,
        .table-scroll-container {
            pointer-events: auto; /* Ensure pointer events work normally */
        }

        /* Prevent any backdrop filters from interfering with user menu */
        .students-table-container {
            isolation: isolate; /* Create new stacking context without affecting z-index */
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-shrink: 0; /* Prevent header from shrinking */
        }

        .table-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e3a8a;
            transition: color 0.3s ease;
        }

        /* Search and Filter Section */
        .search-filter-section {
            flex-shrink: 0; /* Prevent search section from shrinking */
            margin-bottom: 1rem;
        }

        /* Scrollable Table Area */
        .table-scroll-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            background: #ffffff;
            scroll-behavior: smooth;
            position: relative;
            z-index: 2; /* Low z-index, but higher than parent container */
        }

        .table-scroll-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-scroll-container::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .table-scroll-container::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .table-scroll-container::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Scroll shadow indicators */
        .table-scroll-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 5;
        }

        .table-scroll-container.scrolled::before {
            opacity: 1;
        }

        /* Sticky Table Header */
        .students-table thead {
            position: sticky;
            top: 0;
            z-index: 10;
            background: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
        }

        .students-table thead th {
            background: #f8fafc;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.3s ease;
        }

        /* Enhanced header shadow when scrolling */
        .table-scroll-container.scrolled .students-table thead th {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .students-count {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.875rem;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
            transition: all 0.3s ease;
        }

        .students-count:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(30, 58, 138, 0.4);
        }

        .students-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0; /* Remove margin since it's inside scroll container */
        }

        .students-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 0.875rem 1rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .students-table td {
            padding: 0.875rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            vertical-align: middle;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .students-table tr:hover {
            background: rgba(248, 250, 252, 0.8);
            transform: translateX(4px);
            transition: all 0.3s ease;
        }

        .students-table tr:hover td {
            border-color: #cbd5e1;
        }
        
        .assessment-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8125rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .assessment-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .assessment-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .assessment-badge:hover::before {
            left: 100%;
        }

        .assessment-badge:active {
            transform: translateY(-1px);
            transition: transform 0.1s ease;
        }

        .badge-completed {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            color: #166534;
            border-color: #bbf7d0;
            box-shadow: 0 2px 8px rgba(22, 101, 52, 0.2);
        }

        .badge-not-completed {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border-color: #fde68a;
            box-shadow: 0 2px 8px rgba(146, 64, 14, 0.2);
        }

        /* Enhanced badge styles for mathematics assessment performance levels */
        .badge-excellent {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            color: #15803d;
            border-color: #bbf7d0;
            box-shadow: 0 2px 12px rgba(21, 128, 61, 0.25);
        }

        .badge-excellent:hover {
            box-shadow: 0 8px 25px rgba(21, 128, 61, 0.3);
        }

        .badge-good {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            color: #1d4ed8;
            border-color: #bfdbfe;
            box-shadow: 0 2px 12px rgba(29, 78, 216, 0.25);
        }

        .badge-good:hover {
            box-shadow: 0 8px 25px rgba(29, 78, 216, 0.3);
        }

        .badge-fair {
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            color: #d97706;
            border-color: #fde68a;
            box-shadow: 0 2px 12px rgba(217, 119, 6, 0.25);
        }

        .badge-fair:hover {
            box-shadow: 0 8px 25px rgba(217, 119, 6, 0.3);
        }

        .badge-needs-work {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            color: #dc2626;
            border-color: #fca5a5;
            box-shadow: 0 2px 12px rgba(220, 38, 38, 0.25);
        }

        .badge-needs-work:hover {
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
        }

        .badge-pass {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            color: #166534;
            border-color: #bbf7d0;
            box-shadow: 0 2px 8px rgba(22, 101, 52, 0.2);
        }

        .badge-fail {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #dc2626;
            border-color: #fecaca;
            box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
        }
        
        .skeleton-loader {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 6px;
            height: 0.875rem;
        }

        .skeleton-loader.wide {
            width: 100%;
        }

        .skeleton-loader.medium {
            width: 60%;
        }

        .skeleton-loader.narrow {
            width: 40%;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #1e3a8a;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Toast notifications */
        .toast-notification {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            font-size: 0.875rem;
            z-index: 10000;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast-notification.show {
            transform: translateX(0);
        }

        .toast-error {
            background: rgba(239, 68, 68, 0.95);
            color: white;
        }

        .toast-success {
            background: rgba(34, 197, 94, 0.95);
            color: white;
        }
        
        /* Header improvements */
        header {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            transition: all 0.3s ease;
            position: relative;
            z-index: 50; /* Above table content, below user menu */
        }

        header:hover {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        /* Responsive design */
        @media (max-width: 1024px) {
            .dashboard-container {
                padding: 1rem;
            }

            .summary-cards-container {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .summary-card {
                padding: 1.25rem;
            }

            .summary-card-value {
                font-size: 1.75rem;
            }

            .table-title {
                font-size: 1.125rem;
            }

            .students-table-container {
                padding: 1.5rem;
                height: calc(100vh - 350px); /* Adjust for smaller screens */
                min-height: 500px;
                max-height: 700px;
            }

            .table-scroll-container {
                border-radius: 6px;
            }

            .students-table {
                min-width: 1000px;
            }

            .students-table th,
            .students-table td {
                padding: 0.75rem;
                font-size: 0.8125rem;
            }
        }

        @media (max-width: 640px) {
            .dashboard-container {
                padding: 0.75rem;
            }

            .summary-cards-container {
                grid-template-columns: 1fr;
                gap: 0.75rem;
                margin-bottom: 1.25rem;
            }

            .summary-card {
                padding: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 0.75rem;
            }

            .summary-card-icon {
                width: 2.5rem;
                height: 2.5rem;
            }

            .summary-card-icon svg {
                width: 1.25rem;
                height: 1.25rem;
            }

            .summary-card-value {
                font-size: 1.5rem;
            }

            .summary-card-skeleton {
                padding: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 0.75rem;
            }

            .skeleton-card-icon {
                width: 2.5rem;
                height: 2.5rem;
            }

            .table-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
                margin-bottom: 1.5rem;
            }

            .students-table-container {
                padding: 1rem;
                height: calc(100vh - 300px); /* Further adjust for mobile */
                min-height: 400px;
                max-height: 600px;
            }

            .search-filter-section {
                padding: 1rem;
                margin-bottom: 1.5rem;
            }

            .search-filter-row {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .search-bar-container {
                min-width: auto;
                width: 100%;
            }

            .filter-controls {
                flex-direction: column;
                gap: 0.75rem;
                width: 100%;
            }

            .filter-select {
                width: 100%;
                min-width: auto;
            }

            .clear-filters-btn {
                width: 100%;
                justify-content: center;
            }

            .students-table th,
            .students-table td {
                padding: 0.625rem 0.5rem;
                font-size: 0.75rem;
            }

            .assessment-badge {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            .toast-notification {
                top: 0.5rem;
                right: 0.5rem;
                left: 0.5rem;
                font-size: 0.8125rem;
            }
        }

        /* Smooth transitions for all interactive elements */
        * {
            transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
        }

        /* Search and Filter Styles */
        .search-filter-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .search-filter-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-bar-container {
            flex: 1;
            min-width: 300px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            width: 1.25rem;
            height: 1.25rem;
            color: #64748b;
            z-index: 1;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 3rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            font-size: 0.875rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .search-input:focus {
            outline: none;
            border-color: #1e3a8a;
            box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1), 0 4px 8px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 1);
        }

        .clear-search-btn {
            position: absolute;
            right: 0.75rem;
            width: 1.5rem;
            height: 1.5rem;
            color: #64748b;
            background: none;
            border: none;
            cursor: pointer;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .clear-search-btn:hover {
            color: #dc2626;
            background: rgba(220, 38, 38, 0.1);
        }

        .filter-controls {
            display: flex;
            gap: 0.75rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            font-size: 0.875rem;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            min-width: 140px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #1e3a8a;
            box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1), 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .clear-filters-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        .clear-filters-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        }

        .clear-filters-btn:active {
            transform: translateY(0);
        }

        .filter-summary {
            margin-top: 1rem;
            padding: 0.75rem 1rem;
            background: rgba(30, 58, 138, 0.1);
            border: 1px solid rgba(30, 58, 138, 0.2);
            border-radius: 8px;
            font-size: 0.875rem;
            color: #1e3a8a;
        }

        .filter-summary-text {
            font-weight: 500;
        }

        /* Table row animations */
        .table-row-fade-in {
            opacity: 0;
            transform: translateY(10px);
            animation: fadeInUp 0.4s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced table hover effects */
        .students-table tr:hover {
            background: rgba(248, 250, 252, 0.9);
            transform: translateX(4px);
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        /* Loading state for search */
        .search-input.loading {
            background-image: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.1), transparent);
            background-size: 200% 100%;
            animation: searchLoading 1.5s infinite;
        }

        @keyframes searchLoading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* User Menu Additional Styles - Override with higher z-index */
        #user-menu-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 9998 !important; /* Higher than table elements, lower than loading overlay */
            transition: opacity 0.3s ease;
        }

        #user-menu {
            z-index: 9999 !important; /* Same as loading overlay to ensure it's on top */
        }

        #user-menu-button {
            position: relative;
            z-index: 51; /* Above header, below user menu */
        }

        #user-menu-backdrop.hidden {
            display: none;
        }

        /* Ensure backdrop covers all content including scrollable areas */
        #user-menu-backdrop.show {
            opacity: 1;
            visibility: visible;
        }

        #user-menu-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            color: #64748b;
            transition: all 0.3s ease;
        }

        #user-menu-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #374151;
        }

        #user-menu-close svg {
            width: 1.25rem;
            height: 1.25rem;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1.5rem;
            color: #374151;
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f1f5f9;
        }

        .menu-item:hover {
            background: rgba(30, 58, 138, 0.05);
            color: #1e3a8a;
        }

        .credits-section, .subscription-section {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .credits-header, .subscription-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #64748b;
            font-size: 0.875rem;
        }

        .credits-balance, .subscription-status {
            color: #1e3a8a;
            font-weight: 600;
        }

        /* Focus states for accessibility */
        .assessment-badge:focus,
        button:focus,
        a:focus,
        .search-input:focus,
        .filter-select:focus,
        .menu-item:focus {
            outline: 2px solid #1e3a8a;
            outline-offset: 2px;
        }
    </style>
</head>
<body class="simplified-dashboard">
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    <!-- Top Navigation Bar -->
    <header class="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
        <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo Only -->
                <div class="flex items-center">
                    <img src="logo.png" alt="Skills Assess Logo" class="h-8 w-auto">
                </div>

                <!-- User Menu -->
                <div class="relative">
                    <button id="user-menu-button" class="rounded-full p-2" title="Toggle User Menu">
                        <img id="user-menu-avatar" alt="Avatar" class="rounded-full" src="profile.png" width="32" height="32">
                    </button>

                    <!-- User Menu Backdrop -->
                    <div id="user-menu-backdrop" class="hidden"></div>

                    <!-- User Menu Sidebar -->
                    <div id="user-menu" class="hidden">
                        <!-- Close Button -->
                        <div id="user-menu-close">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </div>

                        <!-- User Profile Section -->
                        <div class="px-4 py-2">
                            <div class="flex items-center space-x-3">
                                <div class="avatar-container">
                                    <img id="user-menu-profile-pic" src="profile.png" alt="Profile Picture" class="w-12 h-12 rounded-full">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p id="user-menu-name" class="font-semibold text-gray-900 truncate">Loading...</p>
                                    <p id="user-menu-email" class="text-xs text-gray-500 truncate">Loading...</p>
                                    <p id="user-menu-company" class="text-xs text-gray-400 truncate">Loading...</p>
                                    <div class="inline-flex items-center mt-1">
                                        <span class="text-xs text-green-600">Active</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Credits Section -->
                        <div class="credits-section">
                            <div class="credits-header">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                <span>Credits</span>
                            </div>
                            <div class="credits-balance">
                                <span class="font-semibold">0</span>
                            </div>
                        </div>

                        <!-- Subscription Section -->
                        <div class="subscription-section">
                            <div class="subscription-header">
                                <img src="sparkling.png" alt="Subscription" class="w-4 h-4">
                                <span>Subscription</span>
                            </div>
                            <div class="subscription-status">
                                <span class="font-semibold">Loading...</span>
                            </div>
                        </div>

                        <!-- Edit Profile Option -->
                        <a href="#" id="edit-profile" class="menu-item">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Profile Settings
                        </a>

                        <!-- Logout Option -->
                        <a id="user-logout" href="#" class="menu-item border-t border-gray-200">
                            <img src="logout.png" alt="Logout" class="w-3 h-3 mr-1">
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-20">
        <div class="dashboard-container">
            <!-- Data Summary Cards -->
            <div class="summary-cards-container loading" id="summary-cards-container">
                <!-- Skeleton Loaders -->
                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                    </div>
                </div>

                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                        <div class="skeleton-card-percentage"></div>
                    </div>
                </div>

                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                        <div class="skeleton-card-percentage"></div>
                    </div>
                </div>

                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                        <div class="skeleton-card-percentage"></div>
                    </div>
                </div>

                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                        <div class="skeleton-card-percentage"></div>
                    </div>
                </div>

                <!-- Real Cards -->
                <div class="summary-card" id="total-students-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="total-students-value">0</div>
                        <div class="summary-card-label">Total Students</div>
                    </div>
                </div>

                <div class="summary-card" id="english-completed-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="english-completed-value">0</div>
                        <div class="summary-card-label">English Completed</div>
                        <div class="summary-card-percentage" id="english-completed-percentage">0%</div>
                    </div>
                </div>

                <div class="summary-card" id="math-completed-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="math-completed-value">0</div>
                        <div class="summary-card-label">Math Completed</div>
                        <div class="summary-card-percentage" id="math-completed-percentage">0%</div>
                    </div>
                </div>

                <div class="summary-card" id="digital-skills-completed-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="digital-skills-completed-value">0</div>
                        <div class="summary-card-label">Digital Skills Completed</div>
                        <div class="summary-card-percentage" id="digital-skills-completed-percentage">0%</div>
                    </div>
                </div>

                <div class="summary-card" id="overall-completion-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="overall-completion-value">0%</div>
                        <div class="summary-card-label">Overall Progress</div>
                        <div class="summary-card-sublabel" id="recent-activity">No recent activity</div>
                    </div>
                </div>
            </div>

            <!-- Students Table -->
            <div class="students-table-container">
                <!-- Search and Filter Section -->
                <div class="search-filter-section">
                    <div class="search-filter-row">
                        <!-- Search Bar -->
                        <div class="search-bar-container">
                            <div class="search-input-wrapper">
                                <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <input
                                    type="text"
                                    id="student-search"
                                    placeholder="Search by name or email..."
                                    class="search-input"
                                    autocomplete="off"
                                >
                                <button id="clear-search" class="clear-search-btn" style="display: none;">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Filter Dropdowns -->
                        <div class="filter-controls">
                            <select id="completion-filter" class="filter-select">
                                <option value="all">All Students</option>
                                <option value="english-completed">Completed English</option>
                                <option value="math-completed">Completed Math</option>
                                <option value="digital-skills-completed">Completed Digital Skills</option>
                                <option value="all-completed">Completed All Assessments</option>
                                <option value="none-completed">Not Completed</option>
                            </select>

                            <select id="results-filter" class="filter-select">
                                <option value="all">All Results</option>
                                <option value="english-pass">Passed English</option>
                                <option value="english-fail">Failed English</option>
                                <option value="math-pass">Passed Math</option>
                                <option value="math-fail">Failed Math</option>
                                <option value="digital-skills-high">High Digital Skills</option>
                                <option value="digital-skills-low">Low Digital Skills</option>
                            </select>

                            <button id="clear-filters" class="clear-filters-btn">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Clear Filters
                            </button>
                        </div>
                    </div>

                    <!-- Filter Results Summary -->
                    <div class="filter-summary" id="filter-summary" style="display: none;">
                        <span class="filter-summary-text"></span>
                    </div>
                </div>

                <!-- Scrollable Table Container -->
                <div class="table-scroll-container">
                    <table class="students-table">
                        <thead>
                            <tr>
                                <th>Student Name</th>
                                <th>Email</th>
                                <th>English Assessment</th>
                                <th>Mathematics Assessment</th>
                                <th>Digital Skills Assessment</th>
                                <th>Last Activity</th>
                            </tr>
                        </thead>
                        <tbody id="students-table-body">
                            <!-- Loading skeleton rows -->
                            <tr>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader narrow"></div></td>
                            </tr>
                            <tr>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader narrow"></div></td>
                            </tr>
                            <tr>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader narrow"></div></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts - Load in proper order for dependencies -->
    <script src="feedback.js"></script>
    <script src="courseSelection.js"></script>
    <script src="stripe-handler.js"></script>
    <script src="accountmanagement.js"></script>
    <script src="skills-gap-modal.js"></script>
    <script src="english-results-modal.js"></script>
    <script src="english-assessment-review-modal.js"></script>
    <script src="math-results-modal.js"></script>
    <script src="math-assessment-review-modal.js"></script>
    <script src="enhanced-math-assessment-modal.js"></script>
    <script src="warning-modal.js"></script>
    <script src="feature-access-modal.js"></script>
    <script src="subscription-check.js"></script>
    <script src="subscription-modal.js"></script>
    <script src="topup-modal.js"></script>
    <script src="user-menu.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };

        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
        const db = firebase.firestore();

        // Global variables
        let currentUser = null;
        let userCompany = null;
        let studentsData = [];
        let filteredStudentsData = [];
        let currentSearchTerm = '';
        let currentFilters = {
            completion: 'all',
            results: 'all'
        };

        // Utility functions
        function showLoadingOverlay() {
            document.getElementById('loading-overlay').style.display = 'flex';
        }

        function hideLoadingOverlay() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'Never';

            let date;
            if (timestamp.toDate) {
                date = timestamp.toDate();
            } else if (timestamp instanceof Date) {
                date = timestamp;
            } else {
                date = new Date(timestamp);
            }

            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // English assessment badge generation
        function getEnglishAssessmentBadge(englishData) {
            if (!englishData || !englishData.englishAssessmentCompleted) {
                return {
                    html: '<span class="assessment-badge badge-not-completed">Not Completed</span>',
                    clickable: false
                };
            }

            const score = englishData.englishProficiencyScore || 0;
            const level = englishData.englishProficiencyLevel || 'Entry';
            const isPass = score >= 16;

            const badgeClass = isPass ? 'badge-pass' : 'badge-fail';
            const statusText = `${level} (${score}/21)`;

            return {
                html: `<span class="assessment-badge ${badgeClass} english-badge-clickable"
                             data-user-email="${englishData.userEmail}"
                             data-user-name="${englishData.userName}"
                             title="Click to view detailed English assessment results">
                         ${statusText}
                       </span>`,
                clickable: true
            };
        }

        // Mathematics assessment badge generation - Enhanced for new modal
        function getMathAssessmentBadge(mathData) {
            if (!mathData || !mathData.mathAssessmentCompleted) {
                return {
                    html: '<span class="assessment-badge badge-not-completed" title="Mathematics assessment not completed">Not Completed</span>',
                    clickable: false
                };
            }

            const score = mathData.mathOverallScore || 0;
            const level = mathData.mathHighestLevelCompleted || 'Entry';

            // Determine performance level for better visual indication
            let badgeClass = 'badge-completed';
            let performanceIndicator = '';

            // Calculate approximate percentage based on level completed
            const levelMaxScores = { 'Entry': 44, 'Level1': 70, 'GCSEPart1': 80, 'GCSEPart2': 100 };
            const maxPossible = levelMaxScores[level] || 44;
            const percentage = maxPossible > 0 ? Math.round((score / maxPossible) * 100) : 0;

            if (percentage >= 80) {
                badgeClass = 'badge-excellent';
                performanceIndicator = '🌟';
            } else if (percentage >= 60) {
                badgeClass = 'badge-good';
                performanceIndicator = '✓';
            } else if (percentage >= 40) {
                badgeClass = 'badge-fair';
                performanceIndicator = '⚡';
            } else {
                badgeClass = 'badge-needs-work';
                performanceIndicator = '📈';
            }

            return {
                html: `<span class="assessment-badge ${badgeClass} math-badge-clickable"
                             data-user-email="${mathData.userEmail}"
                             data-user-name="${mathData.userName}"
                             title="Click to view comprehensive Mathematics assessment analysis - ${level} Level (${score} pts, ${percentage}%)">
                         ${performanceIndicator} ${level} (${score} pts)
                       </span>`,
                clickable: true
            };
        }

        // Digital Skills assessment badge generation
        function getDigitalSkillsAssessmentBadge(digitalData) {
            if (!digitalData || !digitalData.digitalSkillsAssessmentCompleted) {
                return {
                    html: '<span class="assessment-badge badge-not-completed" title="Digital Skills assessment not completed">Not Completed</span>',
                    clickable: false
                };
            }

            const score = digitalData.digitalSkillsOverallScore || 0;
            const currentLevel = digitalData.digitalSkillsCurrentLevel || 'EntryLevel2';
            const skillsLevel = digitalData.digitalSkillsSkillsLevel || 'EntryLevel2';

            // Determine performance level based on AI-recommended skills level
            let badgeClass = 'badge-completed';
            let performanceIndicator = '';

            // Map skills levels to performance indicators
            switch (skillsLevel) {
                case 'Level3':
                case 'ICDLLevel3':
                    badgeClass = 'badge-excellent';
                    performanceIndicator = '🌟';
                    break;
                case 'Level2':
                case 'ICDLLevel2':
                    badgeClass = 'badge-excellent';
                    performanceIndicator = '🌟';
                    break;
                case 'Level1':
                    badgeClass = 'badge-good';
                    performanceIndicator = '✓';
                    break;
                case 'EntryLevel3':
                    badgeClass = 'badge-fair';
                    performanceIndicator = '⚡';
                    break;
                case 'EntryLevel2Plus':
                    badgeClass = 'badge-fair';
                    performanceIndicator = '⚡';
                    break;
                case 'EntryLevel2':
                default:
                    badgeClass = 'badge-needs-work';
                    performanceIndicator = '📈';
                    break;
            }

            // Format display level name
            const displayLevel = currentLevel.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim();

            return {
                html: `<span class="assessment-badge ${badgeClass} digital-badge-clickable"
                             data-user-email="${digitalData.userEmail}"
                             data-user-name="${digitalData.userName}"
                             title="Click to view comprehensive Digital Skills assessment analysis - ${displayLevel} (${score} pts, AI Level: ${skillsLevel})">
                         ${performanceIndicator} ${skillsLevel} (${score} pts)
                       </span>`,
                clickable: true
            };
        }

        // Load students data from Firebase
        async function loadStudentsData() {
            try {
                showLoadingOverlay();
                showSummaryCardsLoading();

                if (!userCompany) {
                    throw new Error('No company information available');
                }

                const companyRef = db.collection('companies').doc(userCompany);
                const usersSnapshot = await companyRef.collection('users').get();

                studentsData = [];

                for (const doc of usersSnapshot.docs) {
                    const userData = doc.data();
                    const userEmail = doc.id;

                    // Filter for students only
                    if (userData.userType !== 'student') continue;

                    const studentInfo = {
                        email: userEmail,
                        name: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown',
                        firstName: userData.firstName || '',
                        lastName: userData.lastName || '',

                        // English assessment data
                        english: {
                            englishAssessmentCompleted: userData.englishAssessmentCompleted || false,
                            englishProficiencyScore: userData.englishProficiencyScore || 0,
                            englishProficiencyLevel: userData.englishProficiencyLevel || 'Entry',
                            englishAssessmentTimestamp: userData.englishAssessmentTimestamp,
                            userEmail: userEmail,
                            userName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown'
                        },

                        // Mathematics assessment data
                        math: {
                            mathAssessmentCompleted: userData.mathAssessmentCompleted || false,
                            mathOverallScore: userData.mathOverallScore || 0,
                            mathHighestLevelCompleted: userData.mathHighestLevelCompleted || 'Entry',
                            mathAssessmentTimestamp: userData.mathAssessmentTimestamp,
                            userEmail: userEmail,
                            userName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown'
                        },

                        // Digital Skills assessment data
                        digitalSkills: {
                            digitalSkillsAssessmentCompleted: userData.digitalSkillsAssessmentCompleted || false,
                            digitalSkillsOverallScore: userData.digitalSkillsOverallScore || 0,
                            digitalSkillsCurrentLevel: userData.digitalSkillsCurrentLevel || 'EntryLevel2',
                            digitalSkillsSkillsLevel: userData.digitalSkillsSkillsLevel || 'EntryLevel2',
                            digitalSkillsHighestLevelCompleted: userData.digitalSkillsHighestLevelCompleted || null,
                            digitalSkillsAssessmentTimestamp: userData.digitalSkillsAssessmentTimestamp,
                            userEmail: userEmail,
                            userName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown'
                        },

                        // Activity data
                        lastActivity: userData.lastUpdated || userData.createdAt || null,
                        createdAt: userData.createdAt
                    };

                    studentsData.push(studentInfo);
                }

                // Sort by name
                studentsData.sort((a, b) => a.name.localeCompare(b.name));

                // Initialize filtered data
                filteredStudentsData = [...studentsData];

                renderStudentsTable();
                updateStudentsCount();
                updateSummaryCards();
                initializeSearchAndFilters();

            } catch (error) {
                console.error('Error loading students data:', error);
                showErrorMessage('Failed to load student data. Please refresh the page.');
            } finally {
                hideLoadingOverlay();
            }
        }

        // Render students table
        function renderStudentsTable() {
            const tbody = document.getElementById('students-table-body');

            if (filteredStudentsData.length === 0) {
                const message = studentsData.length === 0
                    ? 'No students found in your organization.'
                    : 'No students match the current search and filter criteria.';

                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-8 text-gray-500">
                            ${message}
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredStudentsData.map((student, index) => {
                const englishBadge = getEnglishAssessmentBadge(student.english);
                const mathBadge = getMathAssessmentBadge(student.math);
                const digitalSkillsBadge = getDigitalSkillsAssessmentBadge(student.digitalSkills);
                const lastActivity = formatDate(student.lastActivity);

                return `
                    <tr style="animation-delay: ${index * 0.05}s" class="table-row-fade-in">
                        <td class="font-medium text-gray-900">${student.name}</td>
                        <td class="text-gray-600">${student.email}</td>
                        <td>${englishBadge.html}</td>
                        <td>${mathBadge.html}</td>
                        <td>${digitalSkillsBadge.html}</td>
                        <td class="text-gray-500">${lastActivity}</td>
                    </tr>
                `;
            }).join('');

            // Add click handlers for assessment badges
            addBadgeClickHandlers();
        }

        // Update students count (now handled by summary cards)
        function updateStudentsCount() {
            // This function is now handled by updateSummaryCards()
            // Keeping for backward compatibility but functionality moved
        }

        // Show summary cards loading state
        function showSummaryCardsLoading() {
            const container = document.getElementById('summary-cards-container');
            if (container) {
                container.classList.add('loading');
            }
        }

        // Hide summary cards loading state
        function hideSummaryCardsLoading() {
            const container = document.getElementById('summary-cards-container');
            if (container) {
                container.classList.remove('loading');
            }
        }

        // Update summary cards with real data
        function updateSummaryCards() {
            const totalStudents = studentsData.length;
            const englishCompleted = studentsData.filter(s => s.english.englishAssessmentCompleted).length;
            const mathCompleted = studentsData.filter(s => s.math.mathAssessmentCompleted).length;
            const digitalSkillsCompleted = studentsData.filter(s => s.digitalSkills.digitalSkillsAssessmentCompleted).length;
            const allThreeCompleted = studentsData.filter(s =>
                s.english.englishAssessmentCompleted &&
                s.math.mathAssessmentCompleted &&
                s.digitalSkills.digitalSkillsAssessmentCompleted
            ).length;

            // Calculate percentages
            const englishPercentage = totalStudents > 0 ? Math.round((englishCompleted / totalStudents) * 100) : 0;
            const mathPercentage = totalStudents > 0 ? Math.round((mathCompleted / totalStudents) * 100) : 0;
            const digitalSkillsPercentage = totalStudents > 0 ? Math.round((digitalSkillsCompleted / totalStudents) * 100) : 0;
            const overallPercentage = totalStudents > 0 ? Math.round((allThreeCompleted / totalStudents) * 100) : 0;

            // Update total students card
            document.getElementById('total-students-value').textContent = totalStudents;

            // Update English completed card
            document.getElementById('english-completed-value').textContent = englishCompleted;
            document.getElementById('english-completed-percentage').textContent = `${englishPercentage}%`;

            // Update Math completed card
            document.getElementById('math-completed-value').textContent = mathCompleted;
            document.getElementById('math-completed-percentage').textContent = `${mathPercentage}%`;

            // Update Digital Skills completed card
            document.getElementById('digital-skills-completed-value').textContent = digitalSkillsCompleted;
            document.getElementById('digital-skills-completed-percentage').textContent = `${digitalSkillsPercentage}%`;

            // Update overall completion card
            document.getElementById('overall-completion-value').textContent = `${overallPercentage}%`;

            // Update recent activity
            const recentActivityElement = document.getElementById('recent-activity');
            if (totalStudents === 0) {
                recentActivityElement.textContent = 'No students enrolled';
            } else if (allThreeCompleted === 0) {
                recentActivityElement.textContent = 'No assessments completed';
            } else {
                const recentCompletions = studentsData.filter(s => {
                    const hasRecent = s.english.englishAssessmentTimestamp || s.math.mathAssessmentTimestamp || s.digitalSkills.digitalSkillsAssessmentTimestamp;
                    if (!hasRecent) return false;

                    const englishDate = s.english.englishAssessmentTimestamp ?
                        (s.english.englishAssessmentTimestamp.toDate ? s.english.englishAssessmentTimestamp.toDate() : new Date(s.english.englishAssessmentTimestamp)) : null;
                    const mathDate = s.math.mathAssessmentTimestamp ?
                        (s.math.mathAssessmentTimestamp.toDate ? s.math.mathAssessmentTimestamp.toDate() : new Date(s.math.mathAssessmentTimestamp)) : null;
                    const digitalSkillsDate = s.digitalSkills.digitalSkillsAssessmentTimestamp ?
                        (s.digitalSkills.digitalSkillsAssessmentTimestamp.toDate ? s.digitalSkills.digitalSkillsAssessmentTimestamp.toDate() : new Date(s.digitalSkills.digitalSkillsAssessmentTimestamp)) : null;

                    const mostRecent = [englishDate, mathDate, digitalSkillsDate]
                        .filter(date => date !== null)
                        .reduce((latest, current) => latest && current ? (latest > current ? latest : current) : (latest || current), null);

                    if (!mostRecent) return false;

                    const daysDiff = (new Date() - mostRecent) / (1000 * 60 * 60 * 24);
                    return daysDiff <= 7; // Within last 7 days
                }).length;

                if (recentCompletions > 0) {
                    recentActivityElement.textContent = `${recentCompletions} completed this week`;
                } else {
                    recentActivityElement.textContent = 'No recent activity';
                }
            }

            // Hide loading state and show real cards
            hideSummaryCardsLoading();

            // Add animation to cards
            const cards = document.querySelectorAll('.summary-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // Search and filter functions
        function filterStudents() {
            let filtered = [...studentsData];

            // Apply search filter
            if (currentSearchTerm.trim()) {
                const searchLower = currentSearchTerm.toLowerCase().trim();
                filtered = filtered.filter(student => {
                    const nameMatch = student.name.toLowerCase().includes(searchLower);
                    const emailMatch = student.email.toLowerCase().includes(searchLower);
                    const firstNameMatch = student.firstName.toLowerCase().includes(searchLower);
                    const lastNameMatch = student.lastName.toLowerCase().includes(searchLower);

                    return nameMatch || emailMatch || firstNameMatch || lastNameMatch;
                });
            }

            // Apply completion filter
            if (currentFilters.completion !== 'all') {
                filtered = filtered.filter(student => {
                    switch (currentFilters.completion) {
                        case 'english-completed':
                            return student.english.englishAssessmentCompleted;
                        case 'math-completed':
                            return student.math.mathAssessmentCompleted;
                        case 'digital-skills-completed':
                            return student.digitalSkills.digitalSkillsAssessmentCompleted;
                        case 'all-completed':
                            return student.english.englishAssessmentCompleted &&
                                   student.math.mathAssessmentCompleted &&
                                   student.digitalSkills.digitalSkillsAssessmentCompleted;
                        case 'none-completed':
                            return !student.english.englishAssessmentCompleted &&
                                   !student.math.mathAssessmentCompleted &&
                                   !student.digitalSkills.digitalSkillsAssessmentCompleted;
                        default:
                            return true;
                    }
                });
            }

            // Apply results filter
            if (currentFilters.results !== 'all') {
                filtered = filtered.filter(student => {
                    switch (currentFilters.results) {
                        case 'english-pass':
                            return student.english.englishAssessmentCompleted && student.english.englishProficiencyScore >= 16;
                        case 'english-fail':
                            return student.english.englishAssessmentCompleted && student.english.englishProficiencyScore < 16;
                        case 'math-pass':
                            return student.math.mathAssessmentCompleted && student.math.mathOverallScore > 0;
                        case 'math-fail':
                            return student.math.mathAssessmentCompleted && student.math.mathOverallScore === 0;
                        case 'digital-skills-high':
                            return student.digitalSkills.digitalSkillsAssessmentCompleted &&
                                   ['Level2', 'Level3', 'ICDLLevel2', 'ICDLLevel3'].includes(student.digitalSkills.digitalSkillsSkillsLevel);
                        case 'digital-skills-low':
                            return student.digitalSkills.digitalSkillsAssessmentCompleted &&
                                   ['EntryLevel2', 'EntryLevel2Plus', 'EntryLevel3', 'Level1'].includes(student.digitalSkills.digitalSkillsSkillsLevel);
                        default:
                            return true;
                    }
                });
            }

            filteredStudentsData = filtered;
            renderStudentsTable();
            updateStudentsCount();
            updateFilterSummary();
        }

        // Update filter summary
        function updateFilterSummary() {
            const summaryElement = document.getElementById('filter-summary');
            const summaryText = summaryElement.querySelector('.filter-summary-text');

            const hasSearch = currentSearchTerm.trim() !== '';
            const hasCompletionFilter = currentFilters.completion !== 'all';
            const hasResultsFilter = currentFilters.results !== 'all';

            if (hasSearch || hasCompletionFilter || hasResultsFilter) {
                let summary = 'Showing ';
                const parts = [];

                if (hasSearch) {
                    parts.push(`search: "${currentSearchTerm}"`);
                }

                if (hasCompletionFilter) {
                    const completionLabels = {
                        'english-completed': 'completed English',
                        'math-completed': 'completed Math',
                        'both-completed': 'completed both assessments',
                        'none-completed': 'no completed assessments'
                    };
                    parts.push(`filter: ${completionLabels[currentFilters.completion]}`);
                }

                if (hasResultsFilter) {
                    const resultsLabels = {
                        'english-pass': 'passed English',
                        'english-fail': 'failed English',
                        'math-pass': 'passed Math',
                        'math-fail': 'failed Math'
                    };
                    parts.push(`results: ${resultsLabels[currentFilters.results]}`);
                }

                summary += parts.join(', ');
                summaryText.textContent = summary;
                summaryElement.style.display = 'block';
            } else {
                summaryElement.style.display = 'none';
            }
        }

        // Initialize search and filter functionality
        function initializeSearchAndFilters() {
            const searchInput = document.getElementById('student-search');
            const clearSearchBtn = document.getElementById('clear-search');
            const completionFilter = document.getElementById('completion-filter');
            const resultsFilter = document.getElementById('results-filter');
            const clearFiltersBtn = document.getElementById('clear-filters');

            // Search input event listener with debouncing
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const value = this.value;

                // Show/hide clear button
                if (value.trim()) {
                    clearSearchBtn.style.display = 'flex';
                } else {
                    clearSearchBtn.style.display = 'none';
                }

                // Debounce search
                searchTimeout = setTimeout(() => {
                    currentSearchTerm = value;
                    filterStudents();
                }, 300);
            });

            // Clear search button
            clearSearchBtn.addEventListener('click', function() {
                searchInput.value = '';
                currentSearchTerm = '';
                this.style.display = 'none';
                filterStudents();
                searchInput.focus();
            });

            // Filter dropdowns
            completionFilter.addEventListener('change', function() {
                currentFilters.completion = this.value;
                filterStudents();
            });

            resultsFilter.addEventListener('change', function() {
                currentFilters.results = this.value;
                filterStudents();
            });

            // Clear filters button
            clearFiltersBtn.addEventListener('click', function() {
                // Reset all filters
                searchInput.value = '';
                completionFilter.value = 'all';
                resultsFilter.value = 'all';
                clearSearchBtn.style.display = 'none';

                // Reset state
                currentSearchTerm = '';
                currentFilters = {
                    completion: 'all',
                    results: 'all'
                };

                // Apply filters
                filterStudents();

                // Focus search input
                searchInput.focus();

                // Show success message
                showSuccessMessage('All filters cleared');
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(event) {
                // Ctrl/Cmd + F to focus search
                if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
                    event.preventDefault();
                    searchInput.focus();
                    searchInput.select();
                }

                // Escape to clear search when focused
                if (event.key === 'Escape' && document.activeElement === searchInput) {
                    if (searchInput.value) {
                        clearSearchBtn.click();
                    } else {
                        searchInput.blur();
                    }
                }
            });
        }

        // Add click handlers for assessment badges
        function addBadgeClickHandlers() {
            // English assessment badge handlers
            document.querySelectorAll('.english-badge-clickable').forEach(badge => {
                badge.addEventListener('click', async function(event) {
                    event.preventDefault();
                    const userEmail = this.getAttribute('data-user-email');
                    const userName = this.getAttribute('data-user-name');

                    console.log('English badge clicked for:', userEmail, userName);

                    // Check if English Results Modal is available
                    if (typeof window.EnglishResultsModal !== 'undefined') {
                        try {
                            showLoadingOverlay();
                            await window.EnglishResultsModal.show({}, userEmail, userName, userCompany);
                            showSuccessMessage('English assessment details loaded');
                        } catch (error) {
                            console.error('Error showing English results modal:', error);
                            showErrorMessage('Failed to load English assessment details');
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else {
                        console.error('EnglishResultsModal not found');
                        showErrorMessage('English assessment modal is not available. Please refresh the page.');
                    }
                });
            });

            // Mathematics assessment badge handlers - Enhanced Modal Integration
            // Prioritizes the Enhanced Mathematics Assessment Modal for comprehensive analytics
            document.querySelectorAll('.math-badge-clickable').forEach(badge => {
                badge.addEventListener('click', async function(event) {
                    event.preventDefault();
                    const userEmail = this.getAttribute('data-user-email');
                    const userName = this.getAttribute('data-user-name');

                    console.log('Math badge clicked for:', userEmail, userName, '- Using Enhanced Modal');

                    // Prioritize Enhanced Math Assessment Modal for admin users
                    const enhancedMathModal = window.EnhancedMathAssessmentModal;
                    const fallbackMathModal = window.MathResultsModal || window.MathAssessmentReviewModal || window.MathematicsResultsModal;

                    if (enhancedMathModal && typeof enhancedMathModal.show === 'function') {
                        try {
                            showLoadingOverlay();
                            await enhancedMathModal.show({}, userEmail, userName, userCompany);
                            showSuccessMessage('Enhanced mathematics assessment analysis loaded');
                        } catch (error) {
                            console.error('Error showing Enhanced Math modal:', error);
                            // Fallback to original modal if enhanced modal fails
                            if (fallbackMathModal && typeof fallbackMathModal.show === 'function') {
                                try {
                                    await fallbackMathModal.show({}, userEmail, userName, userCompany);
                                    showSuccessMessage('Mathematics assessment details loaded');
                                } catch (fallbackError) {
                                    console.error('Error showing fallback Math modal:', fallbackError);
                                    showErrorMessage('Failed to load Mathematics assessment details');
                                }
                            } else {
                                showErrorMessage('Failed to load Mathematics assessment details');
                            }
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else if (fallbackMathModal && typeof fallbackMathModal.show === 'function') {
                        try {
                            showLoadingOverlay();
                            await fallbackMathModal.show({}, userEmail, userName, userCompany);
                            showSuccessMessage('Mathematics assessment details loaded');
                        } catch (error) {
                            console.error('Error showing Math results modal:', error);
                            showErrorMessage('Failed to load Mathematics assessment details');
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else {
                        console.error('Math modal not found. Available:', {
                            EnhancedMathAssessmentModal: typeof window.EnhancedMathAssessmentModal,
                            MathResultsModal: typeof window.MathResultsModal,
                            MathAssessmentReviewModal: typeof window.MathAssessmentReviewModal,
                            MathematicsResultsModal: typeof window.MathematicsResultsModal
                        });
                        showErrorMessage('Mathematics assessment modal is not available. Please refresh the page.');
                    }
                });
            });

            // Digital Skills assessment badge handlers
            document.querySelectorAll('.digital-badge-clickable').forEach(badge => {
                badge.addEventListener('click', async function(event) {
                    event.preventDefault();
                    const userEmail = this.getAttribute('data-user-email');
                    const userName = this.getAttribute('data-user-name');

                    console.log('Digital Skills badge clicked for:', userEmail, userName);

                    // Check if Digital Skills Results Modal is available
                    const digitalSkillsModal = window.DigitalSkillsResultsModal || window.DigitalSkillsAssessmentModal;

                    if (digitalSkillsModal && typeof digitalSkillsModal.show === 'function') {
                        try {
                            showLoadingOverlay();
                            await digitalSkillsModal.show({}, userEmail, userName, userCompany);
                            showSuccessMessage('Digital Skills assessment details loaded');
                        } catch (error) {
                            console.error('Error showing Digital Skills results modal:', error);
                            showErrorMessage('Failed to load Digital Skills assessment details');
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else {
                        console.error('Digital Skills modal not found. Available:', {
                            DigitalSkillsResultsModal: typeof window.DigitalSkillsResultsModal,
                            DigitalSkillsAssessmentModal: typeof window.DigitalSkillsAssessmentModal
                        });
                        showErrorMessage('Digital Skills assessment modal is not available. Please refresh the page.');
                    }
                });
            });
        }

        // Show error message with improved styling
        function showErrorMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'toast-notification toast-error';
            toast.textContent = message;
            document.body.appendChild(toast);

            // Trigger animation
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // Remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }, 5000);
        }

        // Show success message with improved styling
        function showSuccessMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'toast-notification toast-success';
            toast.textContent = message;
            document.body.appendChild(toast);

            // Trigger animation
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // Remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }, 3000);
        }

        // Initialize dashboard
        async function initializeDashboard() {
            try {
                // Wait for Firebase auth to be ready
                await new Promise((resolve) => {
                    firebase.auth().onAuthStateChanged((user) => {
                        if (user) {
                            currentUser = user;
                            resolve();
                        } else {
                            // Redirect to login if not authenticated
                            window.location.href = 'index.html';
                        }
                    });
                });

                // Get admin data to find company
                const adminRef = db.collection('Admins').doc(currentUser.email);
                const adminDoc = await adminRef.get();

                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    userCompany = adminData.company;

                    // Update user menu with real data
                    updateUserMenuData(currentUser, adminData);

                    if (userCompany) {
                        await loadStudentsData();
                    } else {
                        showErrorMessage('No company information found for your account');
                    }
                } else {
                    showErrorMessage('Admin account not found');
                }

            } catch (error) {
                console.error('Error initializing dashboard:', error);
                showErrorMessage('Failed to initialize dashboard');
            }
        }

        // Handle authentication state changes
        firebase.auth().onAuthStateChanged((user) => {
            if (user) {
                currentUser = user;
                console.log('User authenticated:', user.email);
            } else {
                console.log('User not authenticated, redirecting to login');
                window.location.href = 'index.html';
            }
        });

        // Wait for all scripts to load before initializing
        function waitForScripts() {
            return new Promise((resolve) => {
                let checkCount = 0;
                const maxChecks = 50; // 5 seconds max wait

                const checkScripts = () => {
                    checkCount++;
                    console.log('Checking scripts...', {
                        EnglishResultsModal: typeof window.EnglishResultsModal,
                        MathResultsModal: typeof window.MathResultsModal,
                        MathAssessmentReviewModal: typeof window.MathAssessmentReviewModal,
                        EnhancedMathAssessmentModal: typeof window.EnhancedMathAssessmentModal,
                        checkCount
                    });

                    if (typeof window.EnglishResultsModal !== 'undefined' || checkCount >= maxChecks) {
                        resolve();
                    } else {
                        setTimeout(checkScripts, 100);
                    }
                };

                checkScripts();
            });
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('DOM loaded, waiting for scripts...');
            await waitForScripts();
            console.log('Scripts loaded, starting enhanced initialization...');
            enhancedInitialization();
        });

        // Refresh data function
        function refreshData() {
            if (userCompany) {
                showLoadingOverlay();
                loadStudentsData().then(() => {
                    showSuccessMessage('Data refreshed successfully');
                }).catch(() => {
                    showErrorMessage('Failed to refresh data');
                }).finally(() => {
                    hideLoadingOverlay();
                });
            }
        }

        // Add keyboard shortcut for refresh (Ctrl+R or F5)
        document.addEventListener('keydown', function(event) {
            if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
                event.preventDefault();
                refreshData();
            }
        });

        // Debug function to check modal availability
        function checkModalAvailability() {
            const modals = {
                EnglishResultsModal: window.EnglishResultsModal,
                MathResultsModal: window.MathResultsModal,
                MathAssessmentReviewModal: window.MathAssessmentReviewModal,
                EnhancedMathAssessmentModal: window.EnhancedMathAssessmentModal,
                SkillsGapAnalysis: window.SkillsGapAnalysis,
                showSkillsGapAnalysis: window.showSkillsGapAnalysis
            };

            console.log('Modal availability check:', modals);
            return modals;
        }

        // Enhanced initialization with better error handling
        async function enhancedInitialization() {
            try {
                console.log('Starting enhanced initialization...');

                // Check modal availability
                checkModalAvailability();

                // Initialize user menu (from user-menu.js) with delay to ensure DOM is ready
                setTimeout(() => {
                    if (typeof initializeUserMenu === 'function') {
                        console.log('Initializing user menu...');
                        initializeUserMenu();

                        // Verify user menu elements are accessible
                        const userMenuButton = document.getElementById('user-menu-button');
                        const userMenu = document.getElementById('user-menu');
                        const userMenuBackdrop = document.getElementById('user-menu-backdrop');

                        if (userMenuButton && userMenu && userMenuBackdrop) {
                            console.log('User menu elements found and accessible');
                        } else {
                            console.error('User menu elements not found:', {
                                button: !!userMenuButton,
                                menu: !!userMenu,
                                backdrop: !!userMenuBackdrop
                            });
                        }
                    } else {
                        console.error('initializeUserMenu function not found');
                    }
                }, 100);

                // Initialize dashboard
                await initializeDashboard();

                console.log('Dashboard initialized successfully');
                showSuccessMessage('Dashboard loaded successfully');

            } catch (error) {
                console.error('Failed to initialize dashboard:', error);
                showErrorMessage('Failed to initialize dashboard. Please refresh the page.');
            }
        }

        // User menu functionality is handled by user-menu.js

        // Update user menu with real data
        function updateUserMenuData(user, adminData) {
            console.log('Updating user menu data...', adminData);

            // Update user name
            const nameElement = document.getElementById('user-menu-name');
            if (nameElement) {
                nameElement.textContent = `${adminData.firstname || ''} ${adminData.lastname || ''}`.trim() || 'User';
            }

            // Update user email
            const emailElement = document.getElementById('user-menu-email');
            if (emailElement) {
                emailElement.textContent = user.email || 'No email';
            }

            // Update company
            const companyElement = document.getElementById('user-menu-company');
            if (companyElement) {
                companyElement.textContent = adminData.company || 'No company';
            }

            // Update profile picture
            const profilePicElement = document.getElementById('user-menu-profile-pic');
            const avatarElement = document.getElementById('user-menu-avatar');
            const profilePicUrl = user.photoURL || adminData.profilePicture || 'profile.png';

            if (profilePicElement) {
                profilePicElement.src = profilePicUrl;
            }
            if (avatarElement) {
                avatarElement.src = profilePicUrl;
            }

            // Update credits
            const creditsBalance = document.querySelector('.credits-balance .font-semibold');
            if (creditsBalance) {
                creditsBalance.textContent = adminData.credits || 0;
            }

            // Update subscription status
            const subscriptionStatus = document.querySelector('.subscription-status .font-semibold');
            if (subscriptionStatus) {
                const isActive = adminData.subscriptionActive;
                const subscriptionType = adminData.subscriptionType;

                if (isActive && subscriptionType) {
                    if (subscriptionType === 'freeTrial') {
                        subscriptionStatus.textContent = 'Free Trial';
                    } else {
                        subscriptionStatus.textContent = 'Active';
                    }
                } else {
                    subscriptionStatus.textContent = 'Inactive';
                }
            }

            // Set up real-time listener for credits updates
            const adminRef = db.collection('Admins').doc(user.email);
            adminRef.onSnapshot((doc) => {
                if (doc.exists) {
                    const data = doc.data();
                    const creditsElement = document.querySelector('.credits-balance .font-semibold');
                    if (creditsElement) {
                        creditsElement.textContent = data.credits || 0;
                    }
                }
            });
        }

        // Export functions for global access
        window.SimplifiedDashboard = {
            refreshData,
            loadStudentsData,
            showErrorMessage,
            showSuccessMessage,
            checkModalAvailability,
            enhancedInitialization
        };

        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            showErrorMessage('An unexpected error occurred. Please refresh the page.');
        });

        // Initialize scroll shadow effect for table container
        function initializeScrollShadow() {
            const scrollContainer = document.querySelector('.table-scroll-container');
            if (scrollContainer) {
                scrollContainer.addEventListener('scroll', function() {
                    if (this.scrollTop > 0) {
                        this.classList.add('scrolled');
                    } else {
                        this.classList.remove('scrolled');
                    }
                });
            }
        }

        // Initialize scroll shadow when DOM is ready
        document.addEventListener('DOMContentLoaded', initializeScrollShadow);

        // Debug function to check user menu state
        function debugUserMenu() {
            const userMenuButton = document.getElementById('user-menu-button');
            const userMenu = document.getElementById('user-menu');
            const userMenuBackdrop = document.getElementById('user-menu-backdrop');

            console.log('User Menu Debug:', {
                button: {
                    exists: !!userMenuButton,
                    visible: userMenuButton ? getComputedStyle(userMenuButton).display !== 'none' : false,
                    zIndex: userMenuButton ? getComputedStyle(userMenuButton).zIndex : 'N/A'
                },
                menu: {
                    exists: !!userMenu,
                    visible: userMenu ? !userMenu.classList.contains('hidden') : false,
                    zIndex: userMenu ? getComputedStyle(userMenu).zIndex : 'N/A'
                },
                backdrop: {
                    exists: !!userMenuBackdrop,
                    visible: userMenuBackdrop ? !userMenuBackdrop.classList.contains('hidden') : false,
                    zIndex: userMenuBackdrop ? getComputedStyle(userMenuBackdrop).zIndex : 'N/A'
                }
            });
        }

        // Make debug function available globally
        window.debugUserMenu = debugUserMenu;

        // Initialize the dashboard when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, starting dashboard initialization...');
            enhancedInitialization();
        });

        // Global unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            showErrorMessage('An unexpected error occurred. Please refresh the page.');
        });
    </script>
</body>
</html>
