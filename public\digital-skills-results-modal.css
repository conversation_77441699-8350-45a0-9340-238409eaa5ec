/**
 * Digital Skills Assessment Results Modal Styles
 * Matches Skills Gap Analysis modal dimensions and design system
 * Uses 8px spacing grid and existing application theme
 */

/* Modal Overlay */
.modal-overlay#digital-skills-results-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 16px;
}

/* Modal Content Container */
.digital-skills-modal-content {
    background: #ffffff;
    border-radius: 16px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

/* Modal Header */
.digital-skills-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 32px 16px;
    border-bottom: 1px solid #e0e0e0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 16px 16px 0 0;
}

.digital-skills-modal-title-container {
    flex: 1;
}

.digital-skills-modal-student-title {
    color: #333333;
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.digital-skills-modal-subtitle {
    color: #666666;
    font-size: 16px;
    font-weight: 400;
    margin: 0;
    line-height: 1.3;
}

.digital-skills-modal-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.digital-skills-export-button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.digital-skills-export-button:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.digital-skills-close-button {
    background: transparent;
    border: none;
    color: #666666;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.digital-skills-close-button:hover {
    background: #f0f0f0;
    color: #333333;
}

/* Modal Body */
.digital-skills-modal-body {
    padding: 24px 32px 32px;
    color: #333333;
}

/* Section Styling */
.digital-skills-section {
    margin-bottom: 32px;
}

.digital-skills-section:last-child {
    margin-bottom: 0;
}

.digital-skills-section h3 {
    color: #333333;
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #e0e0e0;
}

.digital-skills-section h4 {
    color: #333333;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 12px 0;
}

/* Score Overview Grid */
.digital-skills-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.digital-skills-overview-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: transform 0.2s ease;
}

.digital-skills-overview-card:hover {
    transform: translateY(-2px);
}

.digital-skills-card-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;
}

.digital-skills-card-icon {
    font-size: 20px;
}

.digital-skills-card-title {
    color: #666666;
    font-size: 14px;
    font-weight: 500;
}

.digital-skills-card-value {
    color: #333333;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1;
}

.digital-skills-card-status {
    font-size: 14px;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 20px;
    display: inline-block;
}

.digital-skills-card-status.passed {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.digital-skills-card-status.failed {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid #f44336;
}

.digital-skills-card-subtitle {
    color: #888888;
    font-size: 12px;
    margin-top: 4px;
}

/* Level Progression */
.digital-skills-progression-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.digital-skills-level-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.digital-skills-level-item.passed {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.digital-skills-level-item.failed {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.1);
}

.digital-skills-level-item.current {
    border-color: #2196F3;
    background: rgba(33, 150, 243, 0.1);
}

.digital-skills-level-icon {
    font-size: 24px;
    min-width: 32px;
    text-align: center;
}

.digital-skills-level-info {
    flex: 1;
}

.digital-skills-level-name {
    color: #333333;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
}

.digital-skills-level-status {
    color: #666666;
    font-size: 14px;
}

/* AI Analysis */
.digital-skills-feedback-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.digital-skills-feedback-item {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
}

.digital-skills-feedback-category {
    color: #4CAF50;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.digital-skills-feedback-category::before {
    content: "🤖";
    font-size: 18px;
}

.digital-skills-feedback-text {
    color: #555555;
    font-size: 14px;
    line-height: 1.6;
}

/* Strengths and Improvements */
.digital-skills-strengths-improvements-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.digital-skills-strengths-container,
.digital-skills-improvements-container {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
}

.digital-skills-strengths-title {
    color: #4CAF50;
    margin-bottom: 16px;
}

.digital-skills-improvements-title {
    color: #FF9800;
    margin-bottom: 16px;
}

.digital-skills-strengths-list,
.digital-skills-improvements-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.digital-skills-strength-item,
.digital-skills-improvement-item,
.digital-skills-no-data-item {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.digital-skills-strength-item:last-child,
.digital-skills-improvement-item:last-child,
.digital-skills-no-data-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.digital-skills-strength-item {
    color: #555555;
}

.digital-skills-improvement-item {
    color: #555555;
}

.digital-skills-no-data-item {
    color: #888888;
    font-style: italic;
}

/* Time Analytics */
.digital-skills-time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
}

.digital-skills-time-card {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.digital-skills-time-icon {
    font-size: 24px;
    margin-bottom: 12px;
}

.digital-skills-time-value {
    color: #333333;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
}

.digital-skills-time-label {
    color: #666666;
    font-size: 12px;
}



/* No Data States */
.digital-skills-no-data {
    text-align: center;
    padding: 40px 20px;
    color: #888888;
}

.digital-skills-no-data p {
    font-size: 14px;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .digital-skills-modal-content {
        margin: 8px;
        max-height: calc(100vh - 16px);
    }
    
    .digital-skills-modal-header {
        padding: 16px 20px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .digital-skills-modal-actions {
        align-self: flex-end;
    }
    
    .digital-skills-modal-body {
        padding: 16px 20px 24px;
    }
    
    .digital-skills-overview-grid {
        grid-template-columns: 1fr;
    }
    
    .digital-skills-strengths-improvements-grid {
        grid-template-columns: 1fr;
    }
    

    
    .digital-skills-time-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .digital-skills-modal-student-title {
        font-size: 20px;
    }
    
    .digital-skills-modal-subtitle {
        font-size: 14px;
    }
    
    .digital-skills-section h3 {
        font-size: 18px;
    }
    
    .digital-skills-card-value {
        font-size: 24px;
    }
}
